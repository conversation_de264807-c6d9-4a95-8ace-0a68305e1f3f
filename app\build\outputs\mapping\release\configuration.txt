# The proguard configuration file for the following section is D:\mpd_app_dev\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.3.1
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from D:\mpd_app_dev\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.3.1
# The proguard configuration file for the following section is D:\mpd_app_dev\app\proguard-rules.pro
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# 禁止生成混合大小写的类名
-dontusemixedcaseclassnames
# 指定不去忽略非公共库的类
-dontskipnonpubliclibraryclasses
# 忽略警告信息
-ignorewarnings
# 在混淆过程中输出详细的日志信息
-verbose

# Optimization is turned off by default. Dex does not like code run
# through the ProGuard optimize and preverify steps (and performs some
# of these optimizations on its own).
# 禁用代码优化阶段
-dontoptimize
# 这个过滤器是谷歌推荐的算法
#-optimizations !code/simplification/cast,!field/*,!class/merging/*
# 禁用预验证阶段,Android不需要preverify,去掉这一步能够加快混淆速度
-dontpreverify

# @ref Producing useful obfuscated stack traces http://proguard.sourceforge.net/manual/examples.html#stacktrace
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable

# 避免混淆泛型
-keepattributes Signature

# 保留Annotation不混淆
-keepattributes *Annotation*

# 保留异常相关的信息
-keepattributes Exceptions

# 保留四大组件，因为这些可能会提供给外部使用
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference

# Webview 相关不混淆
-keepclassmembers class * extends android.webkit.WebViewClient {
        public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
        public boolean *(android.webkit.WebView, java.lang.String);
}
-keepclassmembers class * extends android.webkit.WebViewClient {
        public void *(android.webkit.WebView, java.lang.String);
 }

# 保留support下的所有类及其内部类
-keep class android.support.** {*;}
-keep interface android.support.** {*;}
-keep public class * extends android.support.v4.**
-keep public class * extends android.support.v7.**
-keep public class * extends android.support.annotation.**
-dontwarn android.support.**

# 保留androidx下的所有类及其内部类
-keep class androidx.** {*;}
-keep public class * extends androidx.**
-keep interface androidx.** {*;}
-keep class com.google.android.material.** {*;}
-dontwarn androidx.**
-dontwarn com.google.android.material.**
-dontnote com.google.android.material.**

# 保留R下面的资源
-keep class **.R$* {*;}

#表示不混淆任何包含native方法的类的类名以及native方法名
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留在Activity中的方法参数是view的方法，
# 这样以来我们在layout中写的onClick就不会被影响
-keepclassmembers class * extends android.app.Activity{
    public void *(android.view.View);
}

#表示不混淆枚举中的values()和valueOf()方法
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

#表示不混淆Parcelable实现类中的CREATOR字段，包括大小写都不能变，不然整个Parcelable工作机制都会失败。
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# 这指定了继承Serizalizable的类的如下成员不被移除混淆
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

#表示不混淆任何一个View中的setXxx()和getXxx()方法，因为属性动画需要有相应的setter和getter的方法实现，混淆了就无法工作了。
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# Behavior是通过反射调用的，需要保留
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
   *;
}

##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-dontwarn sun.misc.**
#-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson
#-keep class com.google.gson.examples.android.model.** { <fields>; }

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

##---------------End: proguard configuration for Gson  ----------

# Okhttp3的混淆配置
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**
# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*
# OkHttp platform used only on JVM and when Conscrypt dependency is available.
-dontwarn okhttp3.internal.platform.ConscryptPlatform

# OkHttp3
-dontwarn com.squareup.okhttp3.**
-keep class com.squareup.okhttp3.** { *;}
-keep class okhttp3.** { *;}
-dontwarn okio.**

# Okio
-dontwarn com.squareup.**
-dontwarn okio.**
-keep class org.codehaus.** { *; }
-keep class java.nio.** { *; }
-keep class okio.** {*;}

#retrofit2  混淆
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }

#解决报错：升级到 AGP 8.0 后 Retrofit 报错（java.lang.Class cannot be cast to java.lang.reflect.ParameterizedType）
#参考链接：https://github.com/square/retrofit/issues/3751#issuecomment-1192043644
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response
#With R8 full mode generic signatures are stripped for classes that are not
#kept. Suspend functions are wrapped in continuations where the type argument
#is used.
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

#Liveeventbus相关
-dontwarn com.jeremyliao.liveeventbus.**
-keep class com.jeremyliao.liveeventbus.** { *; }
-keep class androidx.lifecycle.** { *; }
-keep class androidx.arch.core.** { *; }

# 保护LiveEventBus事件类，防止反射调用失败
-keep class com.lepu.blepro.event.** { *; }
-keep class com.lepu.blepro.ext.** { *; }
-keep class com.lepu.blepro.objs.** { *; }

#glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
# 如果你的API级别<=Android API 27 则需要添加
#-dontwarn com.bumptech.glide.load.resource.bitmap.VideoDecoder

# keep native method
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保护眼动追踪JNI相关类，防止混淆导致JNI回调失败
-keep class com.airdoc.mpd.gaze.track.GazeTrack {
    *;
}

# 保留 NativeCallback 类 - 这些类被JNI层直接调用，不能混淆
-keep class com.airdoc.mpd.gaze.track.GazeTrack$NativeGazeTrackCallback {
    *;
}

# 检查是否存在GazeApplied相关的JNI回调类
-keep class com.airdoc.mpd.gaze.application.GazeApplied$NativeGazeAppliedCallback {
    *;
}

# 特别保护JNI回调方法，确保方法签名不被混淆
-keepclassmembers class com.airdoc.mpd.gaze.track.GazeTrack$NativeGazeTrackCallback {
    public void onGazeServiceModeChange(int);
    public void onGazeTracking(java.util.HashMap);
    public void onPostureCalibration(java.util.HashMap);
    public void onCalibrating(java.util.HashMap);
    public void onCalibrateCoordinate(boolean, boolean, float, int, int, float, float, int);
}

# 特别保护GazeApplied的JNI回调方法
-keepclassmembers class com.airdoc.mpd.gaze.application.GazeApplied$NativeGazeAppliedCallback {
    public void onGazeAppliedModeChange(int);
}

# 保护眼动追踪相关的数据类，防止Gson序列化/反序列化失败
-keep class com.airdoc.mpd.gaze.bean.** {
    *;
}
-keep class com.airdoc.mpd.gaze.enumeration.** {
    *;
}
-keep class com.airdoc.mpd.device.bean.** {
    *;
}
-keep class com.airdoc.mpd.user.bean.** {
    *;
}
-keep class com.airdoc.mpd.update.bean.** {
    *;
}
-keep class com.airdoc.mpd.media.bean.** {
    *;
}
-keep class com.airdoc.mpd.detection.bean.** {
    *;
}
-keep class com.airdoc.mpd.ppg.bean.** {
    *;
}

# 保护眼动追踪管理类
-keep class com.airdoc.mpd.gaze.GazeTrackingManager {
    *;
}
-keep class com.airdoc.mpd.gaze.track.TrackingManager {
    *;
}

# 保护其他JNI相关类
-keep class com.airdoc.mpd.gaze.application.GazeApplied {
    *;
}
-keep class com.airdoc.mpd.gaze.upload.UploadCloud {
    *;
}

# 保护WebSocket相关类，防止反射调用失败
-keep class com.airdoc.mpd.gaze.track.GazeWebSocketService {
    *;
}
-keep class com.airdoc.mpd.ppg.websocket.PpgWebSocketService {
    *;
}
-keep class com.airdoc.mpd.ppg.websocket.PpgWebSocketManager {
    *;
}

# 保护WebSocket服务器相关类
-keep class org.java_websocket.** {
    *;
}
-dontwarn org.java_websocket.**

# 保护MMKV相关类，防止反射调用失败
-keep class com.tencent.mmkv.** {
    *;
}
-dontwarn com.tencent.mmkv.**

# 保护网络工具类
-keep class com.airdoc.mpd.utils.NetworkUtils {
    *;
}

# 保护相机管理相关类
-keep class com.airdoc.mpd.gaze.track.GTCameraManager {
    *;
}
-keep class com.airdoc.mpd.utils.GTUtils {
    *;
}

#MPAndroidChart
-dontwarn com.github.mikephil.**
-keep class com.github.mikephil.**{ *; }

##---------------Begin: proguard configuration for Alibaba Cloud MQTT ----------
# linkkit API
-keep class com.aliyun.alink.**{*;}
-keep class com.aliyun.linksdk.**{*;}
-dontwarn com.aliyun.**
-dontwarn com.alibaba.**
-dontwarn com.alipay.**
-dontwarn com.ut.**

# keep native method
-keepclasseswithmembernames class * {
    native <methods>;
}

# keep netty
-keepattributes Signature,InnerClasses
-keepclasseswithmembers class io.netty.** {
    *;
}
-keepnames class io.netty.** {
    *;
}
-dontwarn io.netty.**
-dontwarn sun.**

# keep mqtt
-keep public class org.eclipse.paho.**{*;}

# keep fastjson
-dontwarn com.alibaba.fastjson.**
-keep class com.alibaba.fastjson.**{*;}

# keep gson
-keep class com.google.gson.** { *;}

# keep network core
-keep class com.http.**{*;}
-keep class org.mozilla.**{*;}

# keep okhttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-dontwarn org.mozilla.**

-keep class okio.**{*;}
-keep class okhttp3.**{*;}
-keep class org.apache.commons.codec.**{*;}

-keep class com.aliyun.alink.devicesdk.demo.FileProvider{*;}
-keep class android.support.**{*;}
-keep class android.os.**{*;}

##---------------End: proguard configuration for Alibaba Cloud MQTT  ----------

##---------------Begin: LibCommon ----------
-keep class com.airdoc.component.common.net.entity.** {
    *;
}
-keep class com.airdoc.component.common.stat.core.event.bean.** {
    *;
}
##---------------End: LibCommon ----------

-keep class com.airdoc.mpd.update.bean.** {
    *;
}

-keep class com.airdoc.mpd.user.bean.** {
    *;
}

-keep class com.airdoc.mpd.device.bean.** {
    *;
}

-keep class com.airdoc.mpd.media.bean.** {
    *;
}


##---------------Begin: LePu血氧 ----------
-keep class com.lepu.blepro.ext.**{*;}
-keep class com.lepu.blepro.constants.**{*;}
-keep class com.lepu.blepro.event.**{*;}
-keep class com.lepu.blepro.objs.**{*;}
-keep class com.lepu.blepro.utils.DateUtil{*;}
-keep class com.lepu.blepro.utils.HexString{*;}
-keep class com.lepu.blepro.utils.StringUtilsKt{*;}
-keep class com.lepu.blepro.utils.DecompressUtil{*;}
-keep class com.lepu.blepro.utils.FilterUtil{*;}
-keep class com.lepu.blepro.observer.**{*;}
##---------------End: LePu血氧 ----------

##---------------Begin: Stream Log (required by LePu) ----------
-keep class io.getstream.log.**{*;}
-dontwarn io.getstream.log.**
##---------------End: Stream Log ----------

##---------------Begin: CameraX ----------
# CameraX相关类保护
-keep class androidx.camera.** { *; }
-keep interface androidx.camera.** { *; }
-dontwarn androidx.camera.**

# Camera2 API相关类保护
-keep class android.hardware.camera2.** { *; }
-dontwarn android.hardware.camera2.**
##---------------End: CameraX ----------

##---------------Begin: Kotlin Coroutines ----------
# Kotlin协程相关类保护
-keep class kotlinx.coroutines.** { *; }
-dontwarn kotlinx.coroutines.**
-keep class kotlin.coroutines.** { *; }
-dontwarn kotlin.coroutines.**
##---------------End: Kotlin Coroutines ----------

##---------------Begin: Material Manager ----------
# 素材管理器相关类保护
-keep class com.airdoc.mpd.detection.MaterialManager {
    *;
}
-keep class com.airdoc.mpd.detection.MaterialManager$MaterialInfo {
    *;
}
##---------------End: Material Manager ----------

##---------------Begin: Data Cache Manager ----------
# 数据缓存管理器相关类保护
-keep class com.airdoc.mpd.cache.DataCacheManager {
    *;
}
##---------------End: Data Cache Manager ----------

##---------------Begin: Enum Classes ----------
# 保护所有枚举类，防止序列化问题
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
##---------------End: Enum Classes ----------

##---------------Begin: Parcelable ----------
# 保护Parcelable实现类
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
##---------------End: Parcelable ----------

##---------------Begin: Reflection Protection ----------
# 保护可能通过反射调用的类和方法
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# 保护ViewModel相关类
-keep class com.airdoc.mpd.**.vm.** { *; }
-keep class com.airdoc.mpd.**.viewmodel.** { *; }

# 保护Manager单例类
-keep class com.airdoc.mpd.**.*Manager {
    public static ** getInstance();
    public static ** getInstance(...);
    *;
}

# 保护Application类
-keep class com.airdoc.mpd.MpdApplication {
    *;
}

# 保护常量类
-keep class com.airdoc.mpd.**.constants.** { *; }
-keep class com.airdoc.mpd.**.enumeration.** { *; }
-keep class com.airdoc.mpd.gaze.GazeConstants { *; }
-keep class com.airdoc.mpd.common.CommonPreference { *; }

# 保护工具类的公共静态方法
-keepclassmembers class com.airdoc.mpd.utils.** {
    public static <methods>;
}
##---------------End: Reflection Protection ----------

##---------------Begin: Serialization Protection ----------
# 保护序列化相关的字段和方法
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保护Gson序列化字段
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
    @com.google.gson.annotations.Expose <fields>;
}

# 保护Kotlin data class的copy方法和component方法
-keepclassmembers class kotlin.Metadata {
    *;
}
-keepclassmembers class * {
    public ** copy(...);
    public ** component*();
}
##---------------End: Serialization Protection ----------

##---------------Begin: OpenCV Protection ----------
# OpenCV相关类保护
-keep class org.opencv.** { *; }
-dontwarn org.opencv.**
##---------------End: OpenCV Protection ----------

##---------------Begin: ExoPlayer Protection ----------
# ExoPlayer相关类保护
-keep class androidx.media3.** { *; }
-dontwarn androidx.media3.**
-keep class com.google.android.exoplayer2.** { *; }
-dontwarn com.google.android.exoplayer2.**
##---------------End: ExoPlayer Protection ----------

##---------------Begin: WorkManager Protection ----------
# WorkManager相关类保护
-keep class androidx.work.** { *; }
-dontwarn androidx.work.**
##---------------End: WorkManager Protection ----------

##---------------Begin: Barcode Scanning Protection ----------
# 条码扫描相关类保护
-keep class com.google.mlkit.vision.barcode.** { *; }
-dontwarn com.google.mlkit.vision.barcode.**
-keep class com.google.mlkit.vision.face.** { *; }
-dontwarn com.google.mlkit.vision.face.**
##---------------End: Barcode Scanning Protection ----------

##---------------Begin: Custom View Protection ----------
# 保护自定义View类
-keep class com.airdoc.mpd.gaze.widget.** { *; }
-keep class com.airdoc.mpd.**.widget.** { *; }
-keep class com.airdoc.mpd.**.view.** { *; }

# 保护自定义View的构造方法
-keepclassmembers class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
}
##---------------End: Custom View Protection ----------

##---------------Begin: Service Protection ----------
# 保护Service相关类
-keep class com.airdoc.mpd.gaze.track.GazeTrackService {
    *;
}
-keep class com.airdoc.mpd.**.service.** { *; }
##---------------End: Service Protection ----------

##---------------Begin: Debug and Error Handling ----------
# 保护异常处理相关类
-keep class java.lang.Exception { *; }
-keep class java.lang.RuntimeException { *; }
-keep class java.lang.Throwable { *; }

# 保护日志相关类
-keep class com.airdoc.component.common.log.Logger {
    *;
}

# 在Release版本中移除Log调用以减少APK大小
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 但保留错误日志用于崩溃分析
-assumenosideeffects class com.airdoc.component.common.log.Logger {
    public static void v(...);
    public static void i(...);
    public static void d(...);
    public static void w(...);
}

# 保留错误日志
-keep class com.airdoc.component.common.log.Logger {
    public static void e(...);
}
##---------------End: Debug and Error Handling ----------

##---------------Begin: Performance Optimization ----------
# 移除未使用的代码
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn sun.misc.Unsafe

# 优化字符串常量
-optimizations !code/simplification/string

# 注意：行号信息和源文件属性已在文件开头配置，此处不重复
##---------------End: Performance Optimization ----------

# End of content from D:\mpd_app_dev\app\proguard-rules.pro
# The proguard configuration file for the following section is D:\mpd_app_dev\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class androidx.camera.core.impl.MetadataHolderService { <init>(); }
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.airdoc.mpd.MainActivity { <init>(); }
-keep class com.airdoc.mpd.MoreSettingsActivity { <init>(); }
-keep class com.airdoc.mpd.MpdApplication { <init>(); }
-keep class com.airdoc.mpd.config.ConfigActivity { <init>(); }
-keep class com.airdoc.mpd.detection.DetectionActivity { <init>(); }
-keep class com.airdoc.mpd.detection.DetectionActivity1 { <init>(); }
-keep class com.airdoc.mpd.detection.DetectionWebActivity { <init>(); }
-keep class com.airdoc.mpd.detection.hrv.HrvActivity { <init>(); }
-keep class com.airdoc.mpd.gaze.track.GazeTrackService { <init>(); }
-keep class com.airdoc.mpd.scan.ScanActivity { <init>(); }
-keep class com.airdoc.mpd.update.UpdateActivity { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.mlkit.common.internal.MlKitComponentDiscoveryService { <init>(); }
-keep class com.google.mlkit.common.internal.MlKitInitProvider { <init>(); }
-keep class io.getstream.log.android.file.StreamLogFileProvider { <init>(); }
-keep class io.getstream.log.android.file.StreamLogFileService { <init>(); }
-keep class me.jessyan.autosize.InitProvider { <init>(); }
-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SwitchCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.camera.view.PreviewView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.cardview.widget.CardView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.helper.widget.Flow { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.widget.Barrier { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.widget.ConstraintLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.coordinatorlayout.widget.CoordinatorLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.fragment.app.FragmentContainerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.media3.ui.AspectRatioFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.media3.ui.SubtitleView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.media3.ui.TrackSelectionView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.recyclerview.widget.RecyclerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.airdoc.mpd.detection.DetectionWebView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.airdoc.mpd.detection.hrv.HrvWebView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.appbar.MaterialToolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButtonToggleGroup { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.chip.Chip { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.datepicker.MaterialCalendarGridView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.imageview.ShapeableImageView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.BaselineLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.CheckableImageButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.ClippableRoundedCornerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.TouchObserverFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.Snackbar$SnackbarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.SnackbarContentLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputEditText { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ChipTextInputComboView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockFaceView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockHandView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.TimePickerView { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from D:\mpd_app_dev\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\proguard.txt
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
#-keep class com.lepu.blepro.**{*;}
#-keep interface com.lepu.blepro.**{*;}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\proguard.txt
# 保留模型类
-keep class com.airdoc.component.common.net.entity.** {
    *;
}
-keep class com.airdoc.component.common.stat.core.event.bean.** {
    *;
}
# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\09e94a311f42d674eb715371ac8d596c\transformed\material-1.10.0\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior
-keepattributes RuntimeVisible*Annotation*

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# AppCompatViewInflater reads the viewInflaterClass theme attribute which then
# reflectively instantiates MaterialComponentsViewInflater using the no-argument
# constructor. We only need to keep this constructor and the class name if
# AppCompatViewInflater is also being kept.
-if class androidx.appcompat.app.AppCompatViewInflater
-keep class com.google.android.material.theme.MaterialComponentsViewInflater {
    <init>();
}


# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\09e94a311f42d674eb715371ac8d596c\transformed\material-1.10.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This is necessary for default initialization using Camera2Config
-keep public class androidx.camera.camera2.Camera2Config$DefaultProvider { *; }

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\5dcf23016f5fd81271748c4a43ea45ef\transformed\jetified-camera-core-1.4.2\proguard.txt
# Copyright (C) 2023 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keeps the quirk classes to prevent them from being merged incorrectly. For more information,
# please see b/278818703. Preserve Quirk subclasses and their names for correct name mapping in
# QuirkSettings.
-keep,allowshrinking class ** extends androidx.camera.core.impl.Quirk

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\5dcf23016f5fd81271748c4a43ea45ef\transformed\jetified-camera-core-1.4.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\dc2893f89eae3ba97224142d0dcaa4a8\transformed\jetified-barcode-scanning-17.3.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.mlkit_vision_barcode_bundled.zzeh {
  <fields>;
}

# This prevents the names of native methods from being obfuscated and prevents
# UnsatisfiedLinkErrors.
-keepclasseswithmembernames class * {
    native <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\dc2893f89eae3ba97224142d0dcaa4a8\transformed\jetified-barcode-scanning-17.3.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\bf6c3cb5840c02cc9f768a2f9f39e79c\transformed\jetified-face-detection-16.1.7\proguard.txt
# This prevents the names of native methods from being obfuscated and prevents
# UnsatisfiedLinkErrors.
-keepclasseswithmembernames class * {
    native <methods>;
}

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.mlkit_vision_face_bundled.zzuw {
  <fields>;
}

# Uses reflection to determine if these classes are present and has a graceful
# fallback if they aren't.
-dontwarn dalvik.system.VMStack

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\bf6c3cb5840c02cc9f768a2f9f39e79c\transformed\jetified-face-detection-16.1.7\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\proguard.txt
# Annotations are implemented as attributes, so we have to explicitly keep them.
# Catch all which encompasses attributes like RuntimeVisibleParameterAnnotations
# and RuntimeVisibleTypeAnnotations
-keepattributes RuntimeVisible*Annotation*

# JNI is an entry point that's hard to keep track of, so there's
# an annotation to mark fields and methods used by native code.

# Keep the annotations that proguard needs to process.
-keep class com.google.android.apps.common.proguard.UsedBy*

# Just because native code accesses members of a class, does not mean that the
# class itself needs to be annotated - only annotate classes that are
# referenced themselves in native code.
-keep @com.google.android.apps.common.proguard.UsedBy* class * {
  <init>();
}
-keepclassmembers class * {
    @com.google.android.apps.common.proguard.UsedBy* *;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\ede9995337ea73b4d0233d500609b091\transformed\appcompat-1.6.1\proguard.txt
# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# aapt is not able to read app::actionViewClass and app:actionProviderClass to produce proguard
# keep rules. Add a commonly used SearchView to the keep list until b/109831488 is resolved.
-keep class androidx.appcompat.widget.SearchView { <init>(...); }

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl* {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\ede9995337ea73b4d0233d500609b091\transformed\appcompat-1.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\9254240051e84bb84ac5061829e25df2\transformed\jetified-glide-transformations-4.3.0\proguard.txt
-dontwarn jp.co.cyberagent.android.gpuimage.**

-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\9254240051e84bb84ac5061829e25df2\transformed\jetified-glide-transformations-4.3.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\8bd3eae0f86996815ebc23cc6048bb94\transformed\jetified-glide-4.15.1\proguard.txt
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-keep class com.bumptech.glide.load.data.ParcelFileDescriptorRewinder$InternalRewinder {
  *** rewind();
}

# Uncomment for DexGuard only
#-keepresourcexmlelements manifest/application/meta-data@value=GlideModule

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\8bd3eae0f86996815ebc23cc6048bb94\transformed\jetified-glide-4.15.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\e558dea2d0a70667137efa91d48cf54c\transformed\jetified-play-services-tasks-18.2.0\proguard.txt


# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\e558dea2d0a70667137efa91d48cf54c\transformed\jetified-play-services-tasks-18.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.nullness.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\448265791efcfaa1041f3e7728cd0633\transformed\fragment-1.6.1\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# FragmentTransition will reflectively lookup:
# androidx.transition.FragmentTransitionSupport
# We should ensure that we keep the constructor if the code using this is alive
-if class androidx.fragment.app.FragmentTransition {
   private static androidx.fragment.app.FragmentTransitionImpl resolveSupportImpl();
}
-keep class androidx.transition.FragmentTransitionSupport {
    public <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\448265791efcfaa1041f3e7728cd0633\transformed\fragment-1.6.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\fb7343f306845a798c394ec294e9cc5b\transformed\jetified-savedstate-1.2.1\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\fb7343f306845a798c394ec294e9cc5b\transformed\jetified-savedstate-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\proguard.txt
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
# Keep all constructors on ListenableWorker, Worker
-keep public class * extends androidx.work.ListenableWorker {
    public <init>(...);
}
# We need to keep WorkerParameters for the ListenableWorker constructor
-keep class androidx.work.WorkerParameters

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\5f3a99595410223d342783841888f712\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\5f3a99595410223d342783841888f712\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\efd467293e0b9b5a51777a2be79e83eb\transformed\jetified-media3-exoplayer-1.3.1\proguard.txt
# Proguard rules specific to the core module.

# Constructors accessed via reflection in DefaultRenderersFactory
-dontnote androidx.media3.decoder.vp9.LibvpxVideoRenderer
-keepclassmembers class androidx.media3.decoder.vp9.LibvpxVideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.av1.Libgav1VideoRenderer
-keepclassmembers class androidx.media3.decoder.av1.Libgav1VideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.ffmpeg.ExperimentalFfmpegVideoRenderer
-keepclassmembers class androidx.media3.decoder.ffmpeg.ExperimentalFfmpegVideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.opus.LibopusAudioRenderer
-keepclassmembers class androidx.media3.decoder.opus.LibopusAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.flac.LibflacAudioRenderer
-keepclassmembers class androidx.media3.decoder.flac.LibflacAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.ffmpeg.FfmpegAudioRenderer
-keepclassmembers class androidx.media3.decoder.ffmpeg.FfmpegAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.midi.MidiRenderer
-keepclassmembers class androidx.media3.decoder.midi.MidiRenderer {
  <init>(android.content.Context);
}

# Constructors accessed via reflection in DefaultDownloaderFactory
-dontnote androidx.media3.exoplayer.dash.offline.DashDownloader
-keepclassmembers class androidx.media3.exoplayer.dash.offline.DashDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote androidx.media3.exoplayer.hls.offline.HlsDownloader
-keepclassmembers class androidx.media3.exoplayer.hls.offline.HlsDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote androidx.media3.exoplayer.smoothstreaming.offline.SsDownloader
-keepclassmembers class androidx.media3.exoplayer.smoothstreaming.offline.SsDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}

# Constructors accessed via reflection in DefaultMediaSourceFactory
-dontnote androidx.media3.exoplayer.dash.DashMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.dash.DashMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.hls.HlsMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.hls.HlsMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory {
  <init>();
}

# Constructors and methods accessed via reflection in CompositingVideoSinkProvider
-dontnote androidx.media3.effect.PreviewingSingleInputVideoGraph$Factory
-keepclasseswithmembers class androidx.media3.effect.PreviewingSingleInputVideoGraph$Factory {
  <init>(androidx.media3.common.VideoFrameProcessor$Factory);
}
-dontnote androidx.media3.effect.DefaultVideoFrameProcessor$Factory$Builder
-keepclasseswithmembers class androidx.media3.effect.DefaultVideoFrameProcessor$Factory$Builder {
  androidx.media3.effect.DefaultVideoFrameProcessor$Factory build();
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\efd467293e0b9b5a51777a2be79e83eb\transformed\jetified-media3-exoplayer-1.3.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\fc8f3dcb384bdb4af8140e654e58b5f6\transformed\jetified-media3-extractor-1.3.1\proguard.txt
# Proguard rules specific to the extractor module.

# Methods accessed via reflection in DefaultExtractorsFactory
-dontnote androidx.media3.decoder.flac.FlacExtractor
-keepclassmembers class androidx.media3.decoder.flac.FlacExtractor {
  <init>(int);
}
-dontnote androidx.media3.decoder.flac.FlacLibrary
-keepclassmembers class androidx.media3.decoder.flac.FlacLibrary {
  public static boolean isAvailable();
}
-dontnote androidx.media3.decoder.midi.MidiExtractor
-keepclassmembers class androidx.media3.decoder.midi.MidiExtractor {
  <init>();
}

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\fc8f3dcb384bdb4af8140e654e58b5f6\transformed\jetified-media3-extractor-1.3.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\88dbb572ec5c7cefe6d224189160b960\transformed\jetified-media3-datasource-1.3.1\proguard.txt
# Proguard rules specific to the DataSource module.

# Constant folding for resource integers may mean that a resource passed to this method appears to be unused. Keep the method to prevent this from happening.
-keepclassmembers class androidx.media3.datasource.RawResourceDataSource {
  public static android.net.Uri buildRawResourceUri(int);
}

# Constructors accessed via reflection in DefaultDataSource
-dontnote androidx.media3.datasource.rtmp.RtmpDataSource
-keepclassmembers class androidx.media3.datasource.rtmp.RtmpDataSource {
  <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\88dbb572ec5c7cefe6d224189160b960\transformed\jetified-media3-datasource-1.3.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\7a53940ce7983efb289ed3d9246f4a3b\transformed\jetified-media3-common-1.3.1\proguard.txt
# Proguard rules specific to the common module.

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# From https://github.com/google/guava/wiki/UsingProGuardWithGuava
-dontwarn java.lang.ClassValue
-dontwarn java.lang.SafeVarargs
-dontwarn javax.lang.model.element.Modifier
-dontwarn sun.misc.Unsafe

# Don't warn about Guava's compile-only dependencies.
# These lines are needed for ProGuard but not R8.
-dontwarn com.google.errorprone.annotations.**
-dontwarn com.google.j2objc.annotations.**
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Workaround for https://issuetracker.google.com/issues/112297269
# This is needed for ProGuard but not R8.
-keepclassmembernames class com.google.common.base.Function { *; }

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\7a53940ce7983efb289ed3d9246f4a3b\transformed\jetified-media3-common-1.3.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\37eb7cb3503359f8e3891a2adf804078\transformed\jetified-media3-ui-1.3.1\proguard.txt
# Proguard rules specific to the UI module.

# Constructor method accessed via reflection in PlayerView
-dontnote androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView
-keepclassmembers class androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView {
  <init>(android.content.Context);
}
-dontnote androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView
-keepclassmembers class androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView {
  <init>(android.content.Context);
}

# Constructor method accessed via reflection in TrackSelectionDialogBuilder
-dontnote androidx.appcompat.app.AlertDialog.Builder
-keepclassmembers class androidx.appcompat.app.AlertDialog$Builder {
  <init>(android.content.Context, int);
  public android.content.Context getContext();
  public androidx.appcompat.app.AlertDialog$Builder setTitle(java.lang.CharSequence);
  public androidx.appcompat.app.AlertDialog$Builder setView(android.view.View);
  public androidx.appcompat.app.AlertDialog$Builder setPositiveButton(int, android.content.DialogInterface$OnClickListener);
  public androidx.appcompat.app.AlertDialog$Builder setNegativeButton(int, android.content.DialogInterface$OnClickListener);
  public androidx.appcompat.app.AlertDialog create();
}
# Equivalent methods needed when the library is de-jetified.
-dontnote androidx.appcompat.app.AlertDialog.Builder
-keepclassmembers class androidx.appcompat.app.AlertDialog$Builder {
  <init>(android.content.Context, int);
  public android.content.Context getContext();
  public androidx.appcompat.app.AlertDialog$Builder setTitle(java.lang.CharSequence);
  public androidx.appcompat.app.AlertDialog$Builder setView(android.view.View);
  public androidx.appcompat.app.AlertDialog$Builder setPositiveButton(int, android.content.DialogInterface$OnClickListener);
  public androidx.appcompat.app.AlertDialog$Builder setNegativeButton(int, android.content.DialogInterface$OnClickListener);
  public androidx.appcompat.app.AlertDialog create();
}

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\37eb7cb3503359f8e3891a2adf804078\transformed\jetified-media3-ui-1.3.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\a614db70bfd78a9cd3f1844282edd760\transformed\recyclerview-1.3.0\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When layoutManager xml attribute is used, RecyclerView inflates
#LayoutManagers' constructors using reflection.
-keep public class * extends androidx.recyclerview.widget.RecyclerView$LayoutManager {
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
    public <init>();
}

-keepclassmembers class androidx.recyclerview.widget.RecyclerView {
    public void suppressLayout(boolean);
    public boolean isLayoutSuppressed();
}
# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\a614db70bfd78a9cd3f1844282edd760\transformed\recyclerview-1.3.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\48c0bc05c01b91db76e154bf0589d5bb\transformed\coordinatorlayout-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes *Annotation*

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\48c0bc05c01b91db76e154bf0589d5bb\transformed\coordinatorlayout-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\2ee250af5b9082ea1e2e85e68e589d22\transformed\transition-1.2.0\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep a field in transition that is used to keep a reference to weakly-referenced object
-keepclassmembers class androidx.transition.ChangeBounds$* extends android.animation.AnimatorListenerAdapter {
  androidx.transition.ChangeBounds$ViewBounds mViewBounds;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\2ee250af5b9082ea1e2e85e68e589d22\transformed\transition-1.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\c1ccf6ad3c1a3ed7a07102cfe1748e55\transformed\vectordrawable-animated-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\c1ccf6ad3c1a3ed7a07102cfe1748e55\transformed\vectordrawable-animated-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\afc9fcec63ee624454c543e8d82e4d76\transformed\media-1.7.0\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Prevent Parcelable objects from being removed or renamed.
-keep class androidx.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\afc9fcec63ee624454c543e8d82e4d76\transformed\media-1.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\df8e87f1265f72089fb8491f41a438a4\transformed\lifecycle-runtime-2.6.2\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep !interface * implements androidx.lifecycle.LifecycleObserver {
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\df8e87f1265f72089fb8491f41a438a4\transformed\lifecycle-runtime-2.6.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\cef52979e41f21c52c2ea8c565eba0fd\transformed\lifecycle-viewmodel-2.6.2\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\cef52979e41f21c52c2ea8c565eba0fd\transformed\lifecycle-viewmodel-2.6.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\d5a4264e3e52dd1da09e43de9f928404\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\d5a4264e3e52dd1da09e43de9f928404\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\fe4f27493f5c31cc61d72cb1603cc776\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\fe4f27493f5c31cc61d72cb1603cc776\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\6c785514af319d3a819c6c7c18e4ec5f\transformed\rules\lib\META-INF\proguard\retrofit2.pro
# Retrofit does reflection on generic parameters. InnerClasses is required to use Signature and
# EnclosingMethod is required to use InnerClasses.
-keepattributes Signature, InnerClasses, EnclosingMethod

# Retrofit does reflection on method and parameter annotations.
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations

# Keep annotation default values (e.g., retrofit2.http.Field.encoded).
-keepattributes AnnotationDefault

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

# Guarded by a NoClassDefFoundError try/catch and only used when on the classpath.
-dontwarn kotlin.Unit

# Top-level functions that can only be used by Kotlin.
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

# With R8 full mode, it sees no subtypes of Retrofit interfaces since they are created with a Proxy
# and replaces all potential values with null. Explicitly keeping the interfaces prevents this.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

# Keep inherited services.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface * extends <1>

# With R8 full mode generic signatures are stripped for classes that are not
# kept. Suspend functions are wrapped in continuations where the type argument
# is used.
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

# R8 full mode strips generic signatures from return types if not kept.
-if interface * { @retrofit2.http.* public *** *(...); }
-keep,allowoptimization,allowshrinking,allowobfuscation class <3>

# With R8 full mode generic signatures are stripped for classes that are not kept.
-keep,allowobfuscation,allowshrinking class retrofit2.Response

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\6c785514af319d3a819c6c7c18e4ec5f\transformed\rules\lib\META-INF\proguard\retrofit2.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\55382e09b6c582d54890aa18ab320881\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt and other security providers are available.
-dontwarn okhttp3.internal.platform.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\55382e09b6c582d54890aa18ab320881\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\b28b1524e2778f44671542e19c926bed\transformed\jetified-ble-2.2.4\proguard.txt
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\b28b1524e2778f44671542e19c926bed\transformed\jetified-ble-2.2.4\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\e0558eea8bbe483897f828c22b0e5ef0\transformed\jetified-mmkv-static-1.2.16\proguard.txt
# Keep all native methods, their classes and any classes in their descriptors
-keepclasseswithmembers,includedescriptorclasses class com.tencent.mmkv.** {
    native <methods>;
    long nativeHandle;
    private static *** onMMKVCRCCheckFail(***);
    private static *** onMMKVFileLengthError(***);
    private static *** mmkvLogImp(...);
    private static *** onContentChangedByOuterProcess(***);
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\e0558eea8bbe483897f828c22b0e5ef0\transformed\jetified-mmkv-static-1.2.16\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\83cce0b53247cf5607abf9a7fd5167ef\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\83cce0b53247cf5607abf9a7fd5167ef\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\4b5777ed0d4bffa3f904e5b76ee57f60\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\4b5777ed0d4bffa3f904e5b76ee57f60\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\a33641c1b8e8c40de42d22eab0154eb5\transformed\jetified-transport-api-2.2.1\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\a33641c1b8e8c40de42d22eab0154eb5\transformed\jetified-transport-api-2.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\b81fee8ac482d588760b3e70ccd1be41\transformed\jetified-firebase-components-16.1.0\proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\b81fee8ac482d588760b3e70ccd1be41\transformed\jetified-firebase-components-16.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\7be3f3db6c752ace90a9d984f67f1bed\transformed\jetified-firebase-encoders-json-17.1.0\proguard.txt

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\7be3f3db6c752ace90a9d984f67f1bed\transformed\jetified-firebase-encoders-json-17.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\********************************\transformed\room-runtime-2.5.0\proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\********************************\transformed\room-runtime-2.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\00dc443bbfcdc24678a98203a8654113\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\00dc443bbfcdc24678a98203a8654113\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\proguard.txt
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-keep class me.jessyan.autosize.** { *; }
-keep interface me.jessyan.autosize.** { *; }
# End of content from C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\proguard.txt
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>