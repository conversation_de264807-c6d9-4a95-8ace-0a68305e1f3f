# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# 禁止生成混合大小写的类名
-dontusemixedcaseclassnames
# 指定不去忽略非公共库的类
-dontskipnonpubliclibraryclasses
# 忽略警告信息
-ignorewarnings
# 在混淆过程中输出详细的日志信息
-verbose

# Optimization is turned off by default. <PERSON> does not like code run
# through the ProGuard optimize and preverify steps (and performs some
# of these optimizations on its own).
# 禁用代码优化阶段
-dontoptimize
# 这个过滤器是谷歌推荐的算法
#-optimizations !code/simplification/cast,!field/*,!class/merging/*
# 禁用预验证阶段,Android不需要preverify,去掉这一步能够加快混淆速度
-dontpreverify

# @ref Producing useful obfuscated stack traces http://proguard.sourceforge.net/manual/examples.html#stacktrace
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable

# 避免混淆泛型
-keepattributes Signature

# 保留Annotation不混淆
-keepattributes *Annotation*

# 保留异常相关的信息
-keepattributes Exceptions

# 保留四大组件，因为这些可能会提供给外部使用
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference

# Webview 相关不混淆
-keepclassmembers class * extends android.webkit.WebViewClient {
        public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
        public boolean *(android.webkit.WebView, java.lang.String);
}
-keepclassmembers class * extends android.webkit.WebViewClient {
        public void *(android.webkit.WebView, java.lang.String);
 }

# 保护JavaScript接口类和方法
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# 保护WebView中的JavaScript接口内部类
-keep class com.airdoc.mpd.detection.hrv.HrvWebView$HrvAction {
    *;
}
-keep class com.airdoc.mpd.detection.DetectionWebView$DetectionAction {
    *;
}

# 保护所有包含JavascriptInterface注解的方法
-keepclassmembers class * {
    @android.webkit.JavascriptInterface public *;
}

# 保留support下的所有类及其内部类
-keep class android.support.** {*;}
-keep interface android.support.** {*;}
-keep public class * extends android.support.v4.**
-keep public class * extends android.support.v7.**
-keep public class * extends android.support.annotation.**
-dontwarn android.support.**

# 保留androidx下的所有类及其内部类
-keep class androidx.** {*;}
-keep public class * extends androidx.**
-keep interface androidx.** {*;}
-keep class com.google.android.material.** {*;}
-dontwarn androidx.**
-dontwarn com.google.android.material.**
-dontnote com.google.android.material.**

# 保留R下面的资源
-keep class **.R$* {*;}

#表示不混淆任何包含native方法的类的类名以及native方法名
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留在Activity中的方法参数是view的方法，
# 这样以来我们在layout中写的onClick就不会被影响
-keepclassmembers class * extends android.app.Activity{
    public void *(android.view.View);
}

#表示不混淆枚举中的values()和valueOf()方法
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

#表示不混淆Parcelable实现类中的CREATOR字段，包括大小写都不能变，不然整个Parcelable工作机制都会失败。
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# 这指定了继承Serizalizable的类的如下成员不被移除混淆
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

#表示不混淆任何一个View中的setXxx()和getXxx()方法，因为属性动画需要有相应的setter和getter的方法实现，混淆了就无法工作了。
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# Behavior是通过反射调用的，需要保留
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
   *;
}

##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-dontwarn sun.misc.**
#-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson
#-keep class com.google.gson.examples.android.model.** { <fields>; }

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

##---------------End: proguard configuration for Gson  ----------

# Okhttp3的混淆配置
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**
# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*
# OkHttp platform used only on JVM and when Conscrypt dependency is available.
-dontwarn okhttp3.internal.platform.ConscryptPlatform

# OkHttp3
-dontwarn com.squareup.okhttp3.**
-keep class com.squareup.okhttp3.** { *;}
-keep class okhttp3.** { *;}
-dontwarn okio.**

# Okio
-dontwarn com.squareup.**
-dontwarn okio.**
-keep class org.codehaus.** { *; }
-keep class java.nio.** { *; }
-keep class okio.** {*;}

#retrofit2  混淆
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }

#解决报错：升级到 AGP 8.0 后 Retrofit 报错（java.lang.Class cannot be cast to java.lang.reflect.ParameterizedType）
#参考链接：https://github.com/square/retrofit/issues/3751#issuecomment-1192043644
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response
#With R8 full mode generic signatures are stripped for classes that are not
#kept. Suspend functions are wrapped in continuations where the type argument
#is used.
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

#Liveeventbus相关
-dontwarn com.jeremyliao.liveeventbus.**
-keep class com.jeremyliao.liveeventbus.** { *; }
-keep class androidx.lifecycle.** { *; }
-keep class androidx.arch.core.** { *; }

# 保护LiveEventBus事件类，防止反射调用失败
-keep class com.lepu.blepro.event.** { *; }
-keep class com.lepu.blepro.ext.** { *; }
-keep class com.lepu.blepro.objs.** { *; }

#glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
# 如果你的API级别<=Android API 27 则需要添加
#-dontwarn com.bumptech.glide.load.resource.bitmap.VideoDecoder

# keep native method
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保护眼动追踪JNI相关类，防止混淆导致JNI回调失败
-keep class com.airdoc.mpd.gaze.track.GazeTrack {
    *;
}

# 保留 NativeCallback 类 - 这些类被JNI层直接调用，不能混淆
-keep class com.airdoc.mpd.gaze.track.GazeTrack$NativeGazeTrackCallback {
    *;
}

# 检查是否存在GazeApplied相关的JNI回调类
-keep class com.airdoc.mpd.gaze.application.GazeApplied$NativeGazeAppliedCallback {
    *;
}

# 特别保护JNI回调方法，确保方法签名不被混淆
-keepclassmembers class com.airdoc.mpd.gaze.track.GazeTrack$NativeGazeTrackCallback {
    public void onGazeServiceModeChange(int);
    public void onGazeTracking(java.util.HashMap);
    public void onPostureCalibration(java.util.HashMap);
    public void onCalibrating(java.util.HashMap);
    public void onCalibrateCoordinate(boolean, boolean, float, int, int, float, float, int);
}

# 特别保护GazeApplied的JNI回调方法
-keepclassmembers class com.airdoc.mpd.gaze.application.GazeApplied$NativeGazeAppliedCallback {
    public void onGazeAppliedModeChange(int);
}

# 保护眼动追踪相关的数据类，防止Gson序列化/反序列化失败
-keep class com.airdoc.mpd.gaze.bean.** {
    *;
}
-keep class com.airdoc.mpd.gaze.enumeration.** {
    *;
}
-keep class com.airdoc.mpd.device.bean.** {
    *;
}
-keep class com.airdoc.mpd.user.bean.** {
    *;
}
-keep class com.airdoc.mpd.update.bean.** {
    *;
}
-keep class com.airdoc.mpd.media.bean.** {
    *;
}
-keep class com.airdoc.mpd.detection.bean.** {
    *;
}
-keep class com.airdoc.mpd.ppg.bean.** {
    *;
}

# 保护眼动追踪管理类
-keep class com.airdoc.mpd.gaze.GazeTrackingManager {
    *;
}
-keep class com.airdoc.mpd.gaze.track.TrackingManager {
    *;
}

# 保护其他JNI相关类
-keep class com.airdoc.mpd.gaze.application.GazeApplied {
    *;
}
-keep class com.airdoc.mpd.gaze.upload.UploadCloud {
    *;
}

# 保护WebSocket相关类，防止反射调用失败
-keep class com.airdoc.mpd.gaze.track.GazeWebSocketService {
    *;
}
-keep class com.airdoc.mpd.ppg.websocket.PpgWebSocketService {
    *;
}
-keep class com.airdoc.mpd.ppg.websocket.PpgWebSocketManager {
    *;
}

# 保护WebSocket服务器相关类
-keep class org.java_websocket.** {
    *;
}
-dontwarn org.java_websocket.**

# 保护MMKV相关类，防止反射调用失败
-keep class com.tencent.mmkv.** {
    *;
}
-dontwarn com.tencent.mmkv.**

# 保护网络工具类
-keep class com.airdoc.mpd.utils.NetworkUtils {
    *;
}

# 保护相机管理相关类
-keep class com.airdoc.mpd.gaze.track.GTCameraManager {
    *;
}
-keep class com.airdoc.mpd.utils.GTUtils {
    *;
}

#MPAndroidChart
-dontwarn com.github.mikephil.**
-keep class com.github.mikephil.**{ *; }

##---------------Begin: proguard configuration for Alibaba Cloud MQTT ----------
# linkkit API
-keep class com.aliyun.alink.**{*;}
-keep class com.aliyun.linksdk.**{*;}
-dontwarn com.aliyun.**
-dontwarn com.alibaba.**
-dontwarn com.alipay.**
-dontwarn com.ut.**

# keep native method
-keepclasseswithmembernames class * {
    native <methods>;
}

# keep netty
-keepattributes Signature,InnerClasses
-keepclasseswithmembers class io.netty.** {
    *;
}
-keepnames class io.netty.** {
    *;
}
-dontwarn io.netty.**
-dontwarn sun.**

# keep mqtt
-keep public class org.eclipse.paho.**{*;}

# keep fastjson
-dontwarn com.alibaba.fastjson.**
-keep class com.alibaba.fastjson.**{*;}

# keep gson
-keep class com.google.gson.** { *;}

# keep network core
-keep class com.http.**{*;}
-keep class org.mozilla.**{*;}

# keep okhttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-dontwarn org.mozilla.**

-keep class okio.**{*;}
-keep class okhttp3.**{*;}
-keep class org.apache.commons.codec.**{*;}

-keep class com.aliyun.alink.devicesdk.demo.FileProvider{*;}
-keep class android.support.**{*;}
-keep class android.os.**{*;}

##---------------End: proguard configuration for Alibaba Cloud MQTT  ----------

##---------------Begin: LibCommon ----------
-keep class com.airdoc.component.common.net.entity.** {
    *;
}
-keep class com.airdoc.component.common.stat.core.event.bean.** {
    *;
}
# 保护设备工具类，防止获取设备SN失败
-keep class com.airdoc.component.common.utils.DeviceUtils {
    *;
}
# 保护MMKV管理类
-keep class com.airdoc.component.common.cache.MMKVManager {
    *;
}
# 保护日志类
-keep class com.airdoc.component.common.log.Logger {
    *;
}
# 保护网络相关类
-keep class com.airdoc.component.common.net.** {
    *;
}
##---------------End: LibCommon ----------

-keep class com.airdoc.mpd.update.bean.** {
    *;
}

-keep class com.airdoc.mpd.user.bean.** {
    *;
}

-keep class com.airdoc.mpd.device.bean.** {
    *;
}

-keep class com.airdoc.mpd.media.bean.** {
    *;
}

##---------------Begin: Device Management ----------
# 保护设备管理相关类
-keep class com.airdoc.mpd.device.DeviceManager {
    *;
}
-keep class com.airdoc.mpd.device.repository.DeviceRepository {
    *;
}
-keep class com.airdoc.mpd.device.api.DeviceApiService {
    *;
}
-keep class com.airdoc.mpd.device.vm.DeviceViewModel {
    *;
}
-keep class com.airdoc.mpd.device.DeviceReminderManager {
    *;
}

# 保护语言管理类
-keep class com.airdoc.mpd.utils.LanguageManager {
    *;
}

# 保护采集器SN获取相关的关键方法
-keepclassmembers class com.airdoc.mpd.device.DeviceManager {
    public static java.lang.String getDeviceSn();
}
-keepclassmembers class com.airdoc.component.common.utils.DeviceUtils {
    public static java.lang.String getDeviceSerial();
}

# 保护MMKV相关的采集器编号存取方法
-keepclassmembers class com.airdoc.component.common.cache.MMKVManager {
    public static java.lang.String decodeString(...);
    public static void encodeString(...);
    public static java.lang.Boolean decodeBool(...);
    public static void encodeBool(...);
}

# 保护CommonPreference枚举类
-keep enum com.airdoc.mpd.common.CommonPreference {
    *;
}
##---------------End: Device Management ----------

##---------------Begin: LePu血氧 ----------
-keep class com.lepu.blepro.ext.**{*;}
-keep class com.lepu.blepro.constants.**{*;}
-keep class com.lepu.blepro.event.**{*;}
-keep class com.lepu.blepro.objs.**{*;}
-keep class com.lepu.blepro.utils.DateUtil{*;}
-keep class com.lepu.blepro.utils.HexString{*;}
-keep class com.lepu.blepro.utils.StringUtilsKt{*;}
-keep class com.lepu.blepro.utils.DecompressUtil{*;}
-keep class com.lepu.blepro.utils.FilterUtil{*;}
-keep class com.lepu.blepro.observer.**{*;}
##---------------End: LePu血氧 ----------

##---------------Begin: Stream Log (required by LePu) ----------
-keep class io.getstream.log.**{*;}
-dontwarn io.getstream.log.**
##---------------End: Stream Log ----------

##---------------Begin: CameraX ----------
# CameraX相关类保护
-keep class androidx.camera.** { *; }
-keep interface androidx.camera.** { *; }
-dontwarn androidx.camera.**

# Camera2 API相关类保护
-keep class android.hardware.camera2.** { *; }
-dontwarn android.hardware.camera2.**
##---------------End: CameraX ----------

##---------------Begin: Kotlin Coroutines ----------
# Kotlin协程相关类保护
-keep class kotlinx.coroutines.** { *; }
-dontwarn kotlinx.coroutines.**
-keep class kotlin.coroutines.** { *; }
-dontwarn kotlin.coroutines.**
##---------------End: Kotlin Coroutines ----------

##---------------Begin: Material Manager ----------
# 素材管理器相关类保护
-keep class com.airdoc.mpd.detection.MaterialManager {
    *;
}
-keep class com.airdoc.mpd.detection.MaterialManager$MaterialInfo {
    *;
}
##---------------End: Material Manager ----------

##---------------Begin: Data Cache Manager ----------
# 数据缓存管理器相关类保护
-keep class com.airdoc.mpd.cache.DataCacheManager {
    *;
}
##---------------End: Data Cache Manager ----------

##---------------Begin: Enum Classes ----------
# 保护所有枚举类，防止序列化问题
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
##---------------End: Enum Classes ----------

##---------------Begin: Parcelable ----------
# 保护Parcelable实现类
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
##---------------End: Parcelable ----------

##---------------Begin: Reflection Protection ----------
# 保护可能通过反射调用的类和方法
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# 保护ViewModel相关类
-keep class com.airdoc.mpd.**.vm.** { *; }
-keep class com.airdoc.mpd.**.viewmodel.** { *; }

# 保护Manager单例类
-keep class com.airdoc.mpd.**.*Manager {
    public static ** getInstance();
    public static ** getInstance(...);
    *;
}

# 保护Application类
-keep class com.airdoc.mpd.MpdApplication {
    *;
}

# 保护常量类
-keep class com.airdoc.mpd.**.constants.** { *; }
-keep class com.airdoc.mpd.**.enumeration.** { *; }
-keep class com.airdoc.mpd.gaze.GazeConstants { *; }
-keep class com.airdoc.mpd.common.CommonPreference { *; }

# 保护工具类的公共静态方法
-keepclassmembers class com.airdoc.mpd.utils.** {
    public static <methods>;
}
##---------------End: Reflection Protection ----------

##---------------Begin: Serialization Protection ----------
# 保护序列化相关的字段和方法
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保护Gson序列化字段
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
    @com.google.gson.annotations.Expose <fields>;
}

# 保护Kotlin data class的copy方法和component方法
-keepclassmembers class kotlin.Metadata {
    *;
}
-keepclassmembers class * {
    public ** copy(...);
    public ** component*();
}
##---------------End: Serialization Protection ----------

##---------------Begin: JSON Processing ----------
# 保护JSON相关类
-keep class org.json.** { *; }
-dontwarn org.json.**

# 保护JSON处理方法
-keepclassmembers class * {
    public ** toJson(...);
    public ** fromJson(...);
}
##---------------End: JSON Processing ----------

##---------------Begin: WebView JavaScript Interface ----------
# 保护WebView相关类
-keep class com.airdoc.mpd.detection.hrv.HrvWebView {
    *;
}
-keep class com.airdoc.mpd.detection.DetectionWebView {
    *;
}

# 保护JavaScript接口方法，特别是获取初始参数的方法
-keepclassmembers class com.airdoc.mpd.detection.hrv.HrvWebView$HrvAction {
    @android.webkit.JavascriptInterface public java.lang.String getInitialParams();
    @android.webkit.JavascriptInterface public java.lang.String generatePpgReport();
    @android.webkit.JavascriptInterface public void saveAssessmentResult(java.lang.String);
    @android.webkit.JavascriptInterface public java.lang.String getBatchImageUrls(java.lang.String);
    @android.webkit.JavascriptInterface public void startGazeTracking();
    @android.webkit.JavascriptInterface public void stopGazeTracking();
    @android.webkit.JavascriptInterface public void setGazeTrackingEnabled(boolean);
    @android.webkit.JavascriptInterface public void onFinish();
    @android.webkit.JavascriptInterface public void goHome();
    @android.webkit.JavascriptInterface public void printPage();
    @android.webkit.JavascriptInterface public void ready();
}

-keepclassmembers class com.airdoc.mpd.detection.DetectionWebView$DetectionAction {
    @android.webkit.JavascriptInterface public void onFinish();
    @android.webkit.JavascriptInterface public void goHome();
    @android.webkit.JavascriptInterface public void printPage();
    @android.webkit.JavascriptInterface public void startGazeTracking();
    @android.webkit.JavascriptInterface public void stopGazeTracking();
    @android.webkit.JavascriptInterface public void setGazeTrackingEnabled(boolean);
}
##---------------End: WebView JavaScript Interface ----------

##---------------Begin: OpenCV Protection ----------
# OpenCV相关类保护
-keep class org.opencv.** { *; }
-dontwarn org.opencv.**
##---------------End: OpenCV Protection ----------

##---------------Begin: ExoPlayer Protection ----------
# ExoPlayer相关类保护
-keep class androidx.media3.** { *; }
-dontwarn androidx.media3.**
-keep class com.google.android.exoplayer2.** { *; }
-dontwarn com.google.android.exoplayer2.**
##---------------End: ExoPlayer Protection ----------

##---------------Begin: WorkManager Protection ----------
# WorkManager相关类保护
-keep class androidx.work.** { *; }
-dontwarn androidx.work.**
##---------------End: WorkManager Protection ----------

##---------------Begin: Barcode Scanning Protection ----------
# 条码扫描相关类保护
-keep class com.google.mlkit.vision.barcode.** { *; }
-dontwarn com.google.mlkit.vision.barcode.**
-keep class com.google.mlkit.vision.face.** { *; }
-dontwarn com.google.mlkit.vision.face.**
##---------------End: Barcode Scanning Protection ----------

##---------------Begin: Custom View Protection ----------
# 保护自定义View类
-keep class com.airdoc.mpd.gaze.widget.** { *; }
-keep class com.airdoc.mpd.**.widget.** { *; }
-keep class com.airdoc.mpd.**.view.** { *; }

# 保护自定义View的构造方法
-keepclassmembers class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
}
##---------------End: Custom View Protection ----------

##---------------Begin: Service Protection ----------
# 保护Service相关类
-keep class com.airdoc.mpd.gaze.track.GazeTrackService {
    *;
}
-keep class com.airdoc.mpd.**.service.** { *; }
##---------------End: Service Protection ----------

##---------------Begin: Debug and Error Handling ----------
# 保护异常处理相关类
-keep class java.lang.Exception { *; }
-keep class java.lang.RuntimeException { *; }
-keep class java.lang.Throwable { *; }

# 保护日志相关类
-keep class com.airdoc.component.common.log.Logger {
    *;
}

# 在Release版本中移除Log调用以减少APK大小
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 但保留错误日志用于崩溃分析
-assumenosideeffects class com.airdoc.component.common.log.Logger {
    public static void v(...);
    public static void i(...);
    public static void d(...);
    public static void w(...);
}

# 保留错误日志
-keep class com.airdoc.component.common.log.Logger {
    public static void e(...);
}
##---------------End: Debug and Error Handling ----------

##---------------Begin: Performance Optimization ----------
# 移除未使用的代码
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn sun.misc.Unsafe

# 优化字符串常量
-optimizations !code/simplification/string

# 注意：行号信息和源文件属性已在文件开头配置，此处不重复
##---------------End: Performance Optimization ----------
