package com.airdoc.mpd

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.EditText
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.graphics.toColorInt
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.PackageUtils
import com.airdoc.mpd.cache.DataCacheManager
import com.airdoc.mpd.common.CommonPreference
import com.airdoc.mpd.databinding.ActivityMoreSettingsBinding
import com.airdoc.mpd.detection.bean.*
import com.airdoc.mpd.detection.vm.MaterialViewModel
import com.airdoc.mpd.detection.vm.SignalResultViewModel
import com.airdoc.mpd.ppg.PPGManager
import com.airdoc.mpd.ppg.bean.AnalysisResult
import com.airdoc.mpd.ppg.bean.PPGDataPoint
import com.airdoc.mpd.ppg.vm.PpgViewModel
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import kotlinx.coroutines.launch
import com.lepu.blepro.event.EventMsgConst
import com.lepu.blepro.event.InterfaceEvent
import com.lepu.blepro.ext.BleServiceHelper
import com.lepu.blepro.ext.pc60fw.RtParam
import com.lepu.blepro.ext.pc60fw.RtWave
import com.lepu.blepro.objs.Bluetooth
import com.lepu.blepro.objs.BluetoothController
import com.lepu.blepro.observer.BIOL
import com.lepu.blepro.observer.BleChangeObserver
import org.json.JSONArray
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * FileName: MoreSettingsActivity
 * Author by lilin,Date on 2025/7/14 9:53
 * PS: Not easy to write code, please indicate.
 */
class MoreSettingsActivity : BaseCommonActivity() {

    companion object{
        private val TAG = MoreSettingsActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, MoreSettingsActivity::class.java)
            return intent
        }
    }

    private lateinit var binding: ActivityMoreSettingsBinding

    private val gson = Gson()

    // 素材库ViewModel
    private val materialViewModel by viewModels<MaterialViewModel>()

    // 信号结果ViewModel
    private val signalResultViewModel by viewModels<SignalResultViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMoreSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initObserver()
        initView()
    }

    private fun initObserver() {
        // 监听素材库更新结果
        materialViewModel.materialUpdateLiveData.observe(this) { result ->
            when (result) {
                MaterialViewModel.MATERIAL_UPDATE_SUCCESS -> {
                    // 获取最新的素材库信息并更新版本号
                    val materialLibraries = materialViewModel.materialLibrariesLiveData.value
                    if (!materialLibraries.isNullOrEmpty()) {
                        // 获取所有素材库的版本信息，生成综合版本号
                        val versionInfo = generateVersionInfo(materialLibraries)
                        MMKVManager.encodeString(CommonPreference.TEST_VERSION, versionInfo)
                        updateVersionDisplay()
                        Toast.makeText(this, "测试集版本更新成功: $versionInfo", Toast.LENGTH_SHORT).show()
                        Logger.d(TAG, msg = "素材库版本更新成功: $versionInfo")
                    } else {
                        Toast.makeText(this, "测试集版本更新成功", Toast.LENGTH_SHORT).show()
                        Logger.d(TAG, msg = "素材库版本更新成功，但未获取到版本信息")
                    }
                }
                MaterialViewModel.MATERIAL_DOWNLOAD_SUCCESS -> {
                    // 批量下载和解压完成
                    val materialLibraries = materialViewModel.materialLibrariesLiveData.value
                    if (!materialLibraries.isNullOrEmpty()) {
                        val versionInfo = generateVersionInfo(materialLibraries)
                        MMKVManager.encodeString(CommonPreference.TEST_VERSION, versionInfo)
                        updateVersionDisplay()
                        Toast.makeText(this, "所有素材库下载完成，版本更新为: $versionInfo", Toast.LENGTH_LONG).show()
                        Logger.d(TAG, msg = "所有素材库下载完成，版本更新为: $versionInfo")
                    }
                }
                is Pair<*, *> -> {
                    // 更新失败
                    val errorCode = result.first
                    val errorMsg = result.second
                    Toast.makeText(this, "版本更新失败: $errorMsg", Toast.LENGTH_SHORT).show()
                    Logger.e(TAG, msg = "素材库版本更新失败: errorCode=$errorCode, errorMsg=$errorMsg")
                }
                null -> {
                    // 网络错误
                    Toast.makeText(this, "版本更新失败: 网络错误", Toast.LENGTH_SHORT).show()
                    Logger.e(TAG, msg = "素材库版本更新失败: 网络错误")
                }
            }
        }

        // 监听下载状态
        materialViewModel.downloadStatusLiveData.observe(this) { status ->
            if (!status.isNullOrEmpty()) {
                Logger.d(TAG, msg = "下载状态: $status")
                // 只在重要状态变化时显示Toast，避免过多弹窗
                if (status.contains("开始下载") || status.contains("解压完成") || status.contains("错误")) {
                    Toast.makeText(this, status, Toast.LENGTH_SHORT).show()
                }
            }
        }

        // 监听下载进度
        materialViewModel.downloadProgressLiveData.observe(this) { progressPair ->
            val progress = progressPair.first
            val total = progressPair.second
            Logger.d(TAG, msg = "下载进度: ${(progress * 100).toInt()}% (${total}字节)")
            // 可以在这里更新进度条UI
        }

        // 监听下载进度
        materialViewModel.downloadProgressLiveData.observe(this) { (progress, total) ->
            val progressPercent = (progress * 100).toInt()
            Logger.d(TAG, msg = "下载进度: $progressPercent%, 总大小: ${total / 1024 / 1024}MB")
        }

        // 监听信号结果上传结果
        signalResultViewModel.uploadResultLiveData.observe(this) { success ->
            if (success) {
                Logger.d(TAG, msg = "信号结果数据上传成功")
                Toast.makeText(this, "信号结果数据上传成功", Toast.LENGTH_SHORT).show()
            } else {
                Logger.e(TAG, msg = "信号结果数据上传失败")
                Toast.makeText(this, "信号结果数据上传失败", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun initView() {
        initListener()

        // 初始化首页语音引导开关状态
        binding.switchProactivelyGreet.isChecked = MMKVManager.decodeBool(CommonPreference.ENABLE_PROACTIVE_GREETING) == true

        // 初始化指尖式数据采集开关状态
        binding.switchFingertipCollection.isChecked = MMKVManager.decodeBool(CommonPreference.ENABLE_FINGERTIP_DATA_COLLECTION) == true

        // 初始化版本信息 - 现在版本信息直接显示在TextView中
        updateVersionDisplay()

        // 初始化采集器编号 - 现在编号直接显示在TextView中
        updateCollectorNumberDisplay()

        // 初始化数据缓存状态 - 现在缓存状态直接显示在TextView中
        updateDataCacheDisplay()
    }

    private fun initListener() {
        // 返回按钮
        binding.ivBack.setOnClickListener {
            finish()
        }

        // 首页语音引导开关
        binding.switchProactivelyGreet.setOnCheckedChangeListener { buttonView, isChecked ->
            //不是人为点击按钮触发，不处理
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            MMKVManager.encodeBool(CommonPreference.ENABLE_PROACTIVE_GREETING, isChecked)
            Logger.d(TAG, msg = "首页语音引导开关状态改变: $isChecked")
        }

        // 指尖式数据采集开关
        binding.switchFingertipCollection.setOnCheckedChangeListener { buttonView, isChecked ->
            //不是人为点击按钮触发，不处理
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            MMKVManager.encodeBool(CommonPreference.ENABLE_FINGERTIP_DATA_COLLECTION, isChecked)
            Logger.d(TAG, msg = "指尖式数据采集开关状态改变: $isChecked")
        }

        // 模型版本更新按钮
//        binding.btnUpdateModel.setOnClickListener {
//            showUpdateDialog("模型版本") { newVersion ->
//                MMKVManager.encodeString(CommonPreference.MODEL_VERSION, newVersion)
//                updateVersionDisplay()
//                Logger.d(TAG, msg = "模型版本更新为: $newVersion")
//            }
//        }

        // 测试版版本更新按钮 - 调用素材库API获取最新版本
        binding.btnUpdateTest.setOnClickListener {
            Logger.d(TAG, msg = "开始更新测试机版本，调用素材库API")
            Toast.makeText(this, "正在获取最新版本信息...", Toast.LENGTH_SHORT).show()
            materialViewModel.listMaterialLibraries()
        }

        // 数据缓存上传按钮
        binding.btnUploadCache.setOnClickListener {
            // 先取出并打印HRV评估结果数据
//            printHrvAssessmentResults()
            // 然后执行原有的上传逻辑
            uploadDataCache()
        }

        // 采集器编号变更按钮
        binding.btnChangeCollector.setOnClickListener {
            showCollectorNumberDialog()
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        Logger.d(TAG, msg = "Activity销毁，清理PPG超时计时器")
        // 注意：不在这里停止自动上传服务，因为它应该在应用级别管理
        // DataCacheManager.stopAutoUpload()
    }

    /**
     * 更新版本显示
     */
    private fun updateVersionDisplay() {
//        val modelVersion = MMKVManager.decodeString(CommonPreference.MODEL_VERSION) ?: "V1.0.0.1"
        val testVersion = MMKVManager.decodeString(CommonPreference.TEST_VERSION) ?: "V2.0.1"

//        binding.tvModelVersion.text = modelVersion
        binding.tvTestVersion.text = testVersion
//        Logger.d(TAG, msg = "版本信息更新: 模型版本=$modelVersion, 测试版本=$testVersion")
        Logger.d(TAG, msg = "版本信息更新: 测试版本=$testVersion")
    }

    /**
     * 更新采集器编号显示
     */
    private fun updateCollectorNumberDisplay() {
        val collectorNumber = MMKVManager.decodeString(CommonPreference.COLLECTOR_NUMBER) ?: ""
        binding.tvCollectorNumber.text = collectorNumber
        Logger.d(TAG, msg = "采集器编号更新: $collectorNumber")
    }

    /**
     * 更新数据缓存显示
     */
    private fun updateDataCacheDisplay() {
        try {
            val cacheDataCount = DataCacheManager.getCacheDataCount()
            val status = if (cacheDataCount > 0) {
                "${cacheDataCount}条"
            } else {
                "无"
            }
            binding.tvDataCacheStatus.text = status
            Logger.d(TAG, msg = "数据缓存状态更新: $status")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取缓存数据条数异常: ${e.message}")
            binding.tvDataCacheStatus.text = "无"
        }
    }

    /**
     * 更新数据缓存状态 (保留原方法以兼容)
     */
    private fun updateDataCacheStatus() {
        updateDataCacheDisplay()
    }



    /**
     * 获取缓存大小（保留原方法以备后用）
     */
    private fun getCacheSize(): Long {
        var size = 0L
        try {
            // 计算应用缓存目录大小
            size += getDirSize(cacheDir)
            // 计算WebView缓存大小
            val webViewCacheDir = File(cacheDir, "webview")
            if (webViewCacheDir.exists()) {
                size += getDirSize(webViewCacheDir)
            }
            // 计算其他可能的缓存目录
            val webViewDataDir = File(filesDir.parent, "app_webview")
            if (webViewDataDir.exists()) {
                size += getDirSize(webViewDataDir)
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "计算缓存大小异常: ${e.message}")
        }
        return size
    }

    /**
     * 获取目录大小
     */
    private fun getDirSize(dir: File): Long {
        var size = 0L
        try {
            if (dir.exists()) {
                dir.listFiles()?.forEach { file ->
                    size += if (file.isDirectory) {
                        getDirSize(file)
                    } else {
                        file.length()
                    }
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取目录大小异常: ${e.message}")
        }
        return size
    }

    /**
     * 显示版本更新对话框
     */
    private fun showUpdateDialog(title: String, onUpdate: (String) -> Unit) {
        val editText = EditText(this).apply {
            hint = "请输入新版本号，如：V1.0.0.2"
            setPadding(50, 30, 50, 30)
        }

        AlertDialog.Builder(this)
            .setTitle("更新$title")
            .setView(editText)
            .setPositiveButton("确定") { _, _ ->
                val newVersion = editText.text.toString().trim()
                if (newVersion.isNotEmpty()) {
                    if (newVersion.startsWith("V") || newVersion.startsWith("v")) {
                        onUpdate(newVersion)
                        Toast.makeText(this, "${title}更新成功", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this, "版本号格式错误，请以V开头", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this, "版本号不能为空", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示采集器编号变更对话框
     */
    private fun showCollectorNumberDialog() {
        val editText = EditText(this).apply {
            hint = "请输入新的采集器编号，如：000478"
            setText(MMKVManager.decodeString(CommonPreference.COLLECTOR_NUMBER) ?: "000477")
            setPadding(50, 30, 50, 30)
            selectAll()
        }

        AlertDialog.Builder(this)
            .setTitle("变更采集器编号")
            .setView(editText)
            .setPositiveButton("确定") { _, _ ->
                val newNumber = editText.text.toString().trim()
                if (newNumber.isNotEmpty()) {
                    if (newNumber.matches(Regex("\\d{6}"))) {
                        MMKVManager.encodeString(CommonPreference.COLLECTOR_NUMBER, newNumber)
                        updateCollectorNumberDisplay()
                        Logger.d(TAG, msg = "采集器编号变更为: $newNumber")
                        Toast.makeText(this, "采集器编号变更成功", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this, "编号格式错误，请输入6位数字", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this, "编号不能为空", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 上传数据缓存
     */
    private fun uploadDataCache() {
        try {
            Logger.d(TAG, msg = "开始手动上传数据缓存...")

            // 检查是否有本地数据缓存
            val cacheDataCount = DataCacheManager.getCacheDataCount()
            if (cacheDataCount <= 0) {
                Toast.makeText(this, "无本地数据缓存", Toast.LENGTH_SHORT).show()
                Logger.d(TAG, msg = "无本地数据缓存，取消上传")
                return
            }

            // 检查是否正在上传
            if (DataCacheManager.isUploading()) {
                Toast.makeText(this, "正在上传中，请稍后再试", Toast.LENGTH_SHORT).show()
                Logger.d(TAG, msg = "正在上传中，取消本次手动上传")
                return
            }

            // 显示上传中提示
            Toast.makeText(this, "数据缓存上传中...", Toast.LENGTH_SHORT).show()

            // 使用协程进行异步上传
            lifecycleScope.launch {
                // 1. 先上传原有的数据缓存
                val cacheUploadSuccess = DataCacheManager.manualUpload(this@MoreSettingsActivity)

                // 更新缓存状态显示
                updateDataCacheDisplay()

                if (cacheUploadSuccess) {
                    Toast.makeText(this@MoreSettingsActivity, "数据缓存上传完成", Toast.LENGTH_SHORT).show()
                    Logger.d(TAG, msg = "手动上传数据缓存完成")
                } else {
                    Toast.makeText(this@MoreSettingsActivity, "上传失败，请重试", Toast.LENGTH_SHORT).show()
                    Logger.e(TAG, msg = "手动上传数据缓存失败")
                }
            }

        } catch (e: Exception) {
            Logger.e(TAG, msg = "上传数据缓存异常: ${e.message}")
            Toast.makeText(this, "上传失败，请重试", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 清理数据缓存
     */
    private fun clearDataCache() {
        try {
            Logger.d(TAG, msg = "开始清理数据缓存...")

            // 清理HRV评估结果数据
            MMKVManager.encodeString(CommonPreference.HRV_ASSESSMENT_RESULT, null)
            Logger.d(TAG, msg = "已清理HRV评估结果数据")

            // 清理应用缓存目录
            clearDirectory(cacheDir)

            // 清理WebView缓存
            val webViewCacheDir = File(cacheDir, "webview")
            if (webViewCacheDir.exists()) {
                clearDirectory(webViewCacheDir)
            }

            // 清理WebView数据目录
            val webViewDataDir = File(filesDir.parent, "app_webview")
            if (webViewDataDir.exists()) {
                clearDirectory(webViewDataDir)
            }

            // 这里可以添加其他类型的缓存数据清理
            // 例如：其他检测结果、用户数据等

            Logger.d(TAG, msg = "数据缓存清理完成")

        } catch (e: Exception) {
            Logger.e(TAG, msg = "清理数据缓存异常: ${e.message}")
        }
    }

    /**
     * 清理目录
     */
    private fun clearDirectory(dir: File) {
        try {
            if (dir.exists() && dir.isDirectory) {
                dir.listFiles()?.forEach { file ->
                    if (file.isDirectory) {
                        clearDirectory(file)
                    }
                    file.delete()
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "清理目录异常: ${e.message}")
        }
    }

    /**
     * 打印HRV评估结果数据
     */
    private fun printHrvAssessmentResults() {
        try {
            Logger.d(TAG, msg = "📊 开始获取并打印HRV评估结果数据")

            // 从MMKV中获取HRV评估结果数据
            val hrvData = MMKVManager.decodeString(CommonPreference.HRV_ASSESSMENT_RESULT)

            Logger.d(TAG, msg = "打印HRV评估结果数据"+hrvData)

        } catch (e: Exception) {
            Logger.e(TAG, msg = "printHrvAssessmentResults error: ${e.message}")
            Toast.makeText(this, "获取HRV评估结果失败", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 格式化时间戳
     */
    private fun formatTimestamp(timestamp: Long): String {
        return if (timestamp > 0) {
            val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            sdf.format(Date(timestamp))
        } else {
            "未知时间"
        }
    }





    /**
     * 生成综合版本信息
     * 默认使用第一个素材集的版本号
     */
    private fun generateVersionInfo(materialLibraries: List<MaterialLibraryDto>): String {
        return try {
            if (materialLibraries.isEmpty()) {
                "V2.0.1"
            } else {
                // 默认使用第一个素材库的版本号
                materialLibraries.first().version ?: "V2.0.1"
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "生成版本信息失败: ${e.message}")
            "V2.0.1"
        }
    }

}