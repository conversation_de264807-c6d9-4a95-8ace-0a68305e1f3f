package com.airdoc.mpd.detection

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.print.PrintAttributes
import android.print.PrintManager
import android.util.SparseArray
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.OptIn
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.source.ConcatenatingMediaSource
import androidx.media3.exoplayer.source.MediaSource
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.WebViewManager
import com.airdoc.mpd.MoreSettingsActivity
import com.airdoc.mpd.MoreSettingsActivity.Companion
import com.airdoc.mpd.R
import com.airdoc.mpd.common.CommonPreference
import com.airdoc.mpd.detection.hrv.HrvWebView
import com.airdoc.mpd.device.DeviceManager
import com.airdoc.mpd.device.enumeration.StartupMode
import com.airdoc.mpd.gaze.GazeConstants
import com.airdoc.mpd.gaze.track.GazeTrackService
import com.airdoc.mpd.gaze.track.GazeWebSocketService
import com.airdoc.mpd.media.PlayManager
import com.airdoc.mpd.media.bean.RawMedia
import com.airdoc.mpd.ppg.PPGManager
import com.airdoc.mpd.ppg.bean.AnalysisResult
import com.airdoc.mpd.ppg.bean.PPGDataPoint
import com.airdoc.mpd.ppg.vm.PpgViewModel
import com.airdoc.mpd.ppg.websocket.PpgWebSocketManager
import com.airdoc.mpd.user.UserManager
import com.airdoc.mpd.user.bean.DetectionProject
import com.airdoc.mpd.user.bean.User
import com.airdoc.mpd.user.enumeration.Gender
import com.airdoc.mpd.user.vm.UserViewModel
import com.airdoc.mpd.utils.NetworkUtils
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lepu.blepro.constants.Ble
import com.lepu.blepro.event.EventMsgConst
import com.lepu.blepro.event.InterfaceEvent
import com.lepu.blepro.ext.BleServiceHelper
import com.lepu.blepro.ext.pc60fw.RtParam
import com.lepu.blepro.ext.pc60fw.RtWave
import com.lepu.blepro.objs.Bluetooth
import com.lepu.blepro.objs.BluetoothController
import com.lepu.blepro.observer.BIOL
import com.lepu.blepro.observer.BleChangeObserver
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import java.net.InetAddress
import java.net.InetSocketAddress
import java.net.MalformedURLException
import java.net.URL

/**
 * FileName: DetectionActivity1
 * Author by lilin,Date on 2025/5/27 19:25
 * PS: Not easy to write code, please indicate.
 */
class DetectionActivity1 : BaseCommonActivity(), HrvWebView.HrvActionListener,BleChangeObserver {

    companion object{
        private val TAG = DetectionActivity1::class.java.simpleName
        private const val INPUT_PARAM_ACCESS_TOKEN = "accessToken"
        private const val INPUT_PARAM_TOKEN_TYPE = "tokenType"

        const val PARAM_IS_GO_HOME = "IS_GO_HOME"

        fun createIntent(context: Context, accessToken:String, tokenType:String): Intent {
            val intent = Intent(context, DetectionActivity1::class.java)
            intent.putExtra(INPUT_PARAM_ACCESS_TOKEN,accessToken)
            intent.putExtra(INPUT_PARAM_TOKEN_TYPE,tokenType)
            return intent
        }
    }

    private val clDetectionRoot by id<ConstraintLayout>(R.id.cl_detection_root)
    private val ivLogo by id<ImageView>(R.id.iv_logo)
    private val ivUserAvatar by id<ImageView>(R.id.iv_user_avatar)
    private val tvUserName by id<TextView>(R.id.tv_user_name)
    private val tvUserGender by id<TextView>(R.id.tv_user_gender)
    private val tvUserPhone by id<TextView>(R.id.tv_user_phone)
    private val tvDetectionCode by id<TextView>(R.id.tv_detection_code)
    private val tvCancelDetection by id<TextView>(R.id.tv_cancel_detection)
    private val clContent by id<ConstraintLayout>(R.id.cl_content)
    private val wbHrv by id<HrvWebView>(R.id.wb_hrv)
    private val llLoading by id<LinearLayout>(R.id.ll_loading)

    private val userVM by viewModels<UserViewModel>()
    private val ppgVM by viewModels<PpgViewModel>()
    private var projects = mutableListOf<DetectionProject>()
    private var mAccessToken = ""
    private var mTokenType = ""

    // 眼动追踪状态管理
    private var isGazeTrackingActive = false

    // PPG WebSocket管理器
    private val ppgWebSocketManager = PpgWebSocketManager.getInstance()

    private val supportedModels = intArrayOf(Bluetooth.MODEL_PC60FW)

    //数据采集是否完成
    private var isComplete = false

    //数据采集是否终止
    private var isTerminate = false

    private var mPatientId = 0L

    private val ppgDataPointList = mutableListOf<PPGDataPoint>()


    //数据分析结果
    private var ppgAnalysisResult: AnalysisResult? = null

    private val gson = Gson()


    // 是否已经开始数据采集
    private var isDataCollectionStarted = false



    private val handler = object :  LifecycleHandler(Looper.getMainLooper(),this) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }

    private fun parseMessage(msg: Message) {
        when(msg.what) {
            GazeConstants.MSG_GAZE_TRACKING_STATE -> {
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "收到眼动追踪状态更新: $state")

                lifecycleScope.launch {
                    if (state) {
                        // 眼动追踪启动成功
                        isGazeTrackingActive = true
                    } else {
                        // 眼动追踪停止
                        isGazeTrackingActive = false
                    }
                }
            }
            else -> {
                Logger.d(TAG, msg = "收到未处理的消息: ${msg.what}")
            }
        }
    }


    var mServiceManager : Messenger? = null

    private var mClientMessage: Messenger = Messenger(handler)


    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "DetectionActivity1 onServiceConnected")
            if (service != null){
                mServiceManager = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceManager?.send(message)

                // 服务连接成功后立即发送开启相机消息
                Logger.d(TAG, msg = "📷 服务连接成功，发送开启相机消息")
                val cameraMessage = Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_ON_CAMERA
                    replyTo = mClientMessage
                }
                mServiceManager?.send(cameraMessage)
                Logger.d(TAG, msg = "✓ 已发送开启相机消息")
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "DetectionActivity1 onServiceDisconnected")
            mServiceManager = null
        }
    }


    //检测Web页启动器
    private var detectionWebLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            val data = result.data
            val isGoHome = data?.getBooleanExtra(PARAM_IS_GO_HOME,false)?:false
            if (isGoHome){
                finish()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WebViewManager.hookWebView()
        setContentView(R.layout.activity_detection1)

        // 启动眼动追踪前台服务（但不在服务中自动启动相机和追踪）
        startForegroundService(Intent(this, GazeTrackService::class.java))

        this.bindService(Intent(this, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)

        // 设置返回键处理
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                handleBackPress()
            }
        })

        initParam()
        initView()
        initObserver()
        initData()

        // 启动PPG WebSocket服务
        startPpgWebSocketService()
    }

    private fun initParam() {
        mAccessToken = intent.getStringExtra(INPUT_PARAM_ACCESS_TOKEN) ?: ""
        mTokenType = intent.getStringExtra(INPUT_PARAM_TOKEN_TYPE) ?: ""
    }

    private fun initView() {
        initListener()

        val logo = DeviceManager.getDeviceInfo()?.logo
        if (!logo.isNullOrEmpty()){
            ImageLoader.loadImageWithPlaceholder(this,logo,0,R.drawable.ic_main_logo,ivLogo)
        }else{
            ivLogo.setImageResource(R.drawable.ic_main_logo)
        }

        // 添加原有的android接口
        wbHrv.addJavascriptInterface(wbHrv.HrvAction(),"android")

        wbHrv.setHrvActionListener(this)

        wbHrv.setBackgroundColor(Color.TRANSPARENT)

        clearWebViewCache()

    }

    private fun initListener() {
        tvCancelDetection.setOnSingleClickListener {
            finish()
        }
    }

    /**
     * 处理返回键事件，确保在退出前关闭眼动追踪
     */
    private fun handleBackPress() {
        Logger.d(TAG, msg = "🔙 DetectionActivity1 - 收到返回键事件")
        Logger.d(TAG, msg = "  当前眼动追踪状态: $isGazeTrackingActive")

        // 如果眼动追踪正在运行，先停止眼动追踪
        if (isGazeTrackingActive) {
            Logger.d(TAG, msg = "  眼动追踪正在运行，先停止眼动追踪再退出")
            lifecycleScope.launch {
                try {
                    // 调用停止眼动追踪方法
                    onStopGazeTracking()
                    Logger.d(TAG, msg = "  ✓ 眼动追踪已停止，延迟后退出Activity")

                    // 解绑服务连接
                    try {
                        Logger.d(TAG, msg = "  解绑GazeTrackService服务连接")
                        unbindService(serviceConnection)
                        mServiceManager = null
                        Logger.d(TAG, msg = "  ✓ GazeTrackService服务连接已解绑")
                    } catch (e: Exception) {
                        Logger.e(TAG, msg = "  解绑GazeTrackService服务连接异常: ${e.message}")
                    }

                    // 停止GazeTrackService前台服务以释放资源
                    try {
                        Logger.d(TAG, msg = "  停止GazeTrackService前台服务")
                        stopService(Intent(this@DetectionActivity1, GazeTrackService::class.java))
                        Logger.d(TAG, msg = "  ✓ GazeTrackService服务已停止")
                    } catch (e: Exception) {
                        Logger.e(TAG, msg = "  停止GazeTrackService服务异常: ${e.message}")
                    }

                    // 使用finish()来正常退出
                    finish()
                    Logger.d(TAG, msg = "  ✓ Activity已退出")
                } catch (e: Exception) {
                    Logger.e(TAG, msg = "  停止眼动追踪异常: ${e.message}")
                    // 即使停止失败也要退出Activity
                    finish()
                }
            }
        } else {
            Logger.d(TAG, msg = "  眼动追踪未运行，直接退出Activity")

            // 解绑服务连接
            try {
                Logger.d(TAG, msg = "  解绑GazeTrackService服务连接")
                unbindService(serviceConnection)
                mServiceManager = null
                Logger.d(TAG, msg = "  ✓ GazeTrackService服务连接已解绑")
            } catch (e: Exception) {
                Logger.e(TAG, msg = "  解绑GazeTrackService服务连接异常: ${e.message}")
            }

            // 停止GazeTrackService前台服务以释放资源
            try {
                Logger.d(TAG, msg = "  停止GazeTrackService前台服务")
                stopService(Intent(this, GazeTrackService::class.java))
                Logger.d(TAG, msg = "  ✓ GazeTrackService服务已停止")
            } catch (e: Exception) {
                Logger.e(TAG, msg = "  停止GazeTrackService服务异常: ${e.message}")
            }
            // 眼动追踪未运行，直接退出
            finish()
        }
    }

    private fun initObserver() {
        userVM.userLiveData.observe(this){
            updateUserInfo(it)
        }
        userVM.detectionProjectsLiveData.observe(this){
            projects.addAll(it?.projects?: emptyList())
            if (projects.isNotEmpty()){
                val detectionProject = projects[0]
                Logger.d(TAG, msg = "detectionProjectsLiveData detectionProject = $detectionProject")
                val url = detectionProject.url?:""
//                val url = "http://10.100.2.199:5173/hrvtest"
                val code = detectionProject.code?:""
                if (code == "HRV"){
                    loadUrl(url)
                }
            }
        }


        // 监听服务初始化完成
        LiveEventBus.get<Boolean>(EventMsgConst.Ble.EventServiceConnectedAndInterfaceInit)
            .observeSticky(this) {
                startScan()
            }

        // 监听设备发现
        LiveEventBus.get<Bluetooth>(EventMsgConst.Discovery.EventDeviceFound)
            .observe(this) { bluetooth ->
                // 处理发现的设备
                handleDeviceFound(bluetooth)
            }

        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtParam).observe(this) {
            val data = it.data
            Logger.e(TAG, msg = "EventPC60FwRtParam data = $data")
            if (data is RtParam){
                // 广播RtParam数据到WebSocket客户端
                broadcastRtParamData(data)
            }
        }


        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtWave).observe(this) {
            val data = it.data
            if (data is RtWave){
                Logger.e(TAG, msg = "EventPC60FwRtWave waveIntData = ${gson.toJson(data.waveIntData)}")
                if (!isComplete && !isTerminate){
                    // 标记数据采集已开始
                    if (!isDataCollectionStarted) {
                        isDataCollectionStarted = true
                        Logger.d(TAG, msg = "PPG数据采集开始")
                    }


                    val ints = data.waveIntData.toList()
                    ints.forEachIndexed { index, value ->
                        val ppgDataPoint = if (ppgDataPointList.isEmpty()){
                            PPGDataPoint(value.toDouble(), System.currentTimeMillis() * 1_000_000L + System.nanoTime() % 1_000_000L)
                        }else{
                            //间隔20毫秒，转成纳秒
                            PPGDataPoint(value.toDouble(),ppgDataPointList[ppgDataPointList.size - 1].timestamp + 20 * 1_000_000L)
                        }
                        ppgDataPointList.add(ppgDataPoint)

                        // 广播PPG数据到WebSocket客户端
                        broadcastPpgData(ppgDataPoint)
                    }
                }
            }
        }
    }

    private fun startScan() {
        Logger.e(TAG, TAG,"开始扫描")

        BleServiceHelper.BleServiceHelper.startScan(supportedModels)
    }

    private fun handleDeviceFound(bluetooth: Bluetooth) {
        // 检查指尖式数据采集开关是否开启
        val isFingertipCollectionEnabled = MMKVManager.decodeBool(CommonPreference.ENABLE_FINGERTIP_DATA_COLLECTION) ?: true

        if (!isFingertipCollectionEnabled) {
            Logger.w(TAG, msg = "指尖式数据采集已关闭，拒绝连接PPG设备: ${bluetooth.name}")
            Toast.makeText(this, "指尖式数据采集已关闭，无法连接设备", Toast.LENGTH_SHORT).show()
            return
        }

        // 获取采集器编号
        val collectorNumber = MMKVManager.decodeString(CommonPreference.COLLECTOR_NUMBER) ?: ""
        Logger.d(TAG, msg = "当前采集器编号: $collectorNumber")

        val devices = BluetoothController.getDevices()
        Logger.e(TAG, TAG, "发现的设备列表: $devices")

        // 遍历所有设备，检查采集器编号和设备名称后六位数字是否一致
        var targetDevice: Bluetooth? = null
        for (device in devices) {
            Logger.d(TAG, msg = "检查设备: ${device.name}")

            // 提取设备名称中的后六位数字
            val deviceNameLastSixDigits = extractLastSixDigits(device.name)
            Logger.d(TAG, msg = "设备名称后六位数字: $deviceNameLastSixDigits")

            // 比较采集器编号和设备名称后六位数字
            if (collectorNumber.isNotEmpty() && deviceNameLastSixDigits.isNotEmpty()) {
                if (collectorNumber == deviceNameLastSixDigits) {
                    Logger.d(TAG, msg = "✓ 设备验证通过: ${device.name}, 采集器编号匹配: $collectorNumber")
                    targetDevice = device
                    break
                } else {
                    Logger.w(TAG, msg = "✗ 设备验证失败: ${device.name}, 采集器编号: $collectorNumber, 设备后六位: $deviceNameLastSixDigits")
                }
            } else {
                Logger.w(TAG, msg = "✗ 验证信息不完整: 采集器编号: '$collectorNumber', 设备后六位: '$deviceNameLastSixDigits'")
            }
        }

        if (targetDevice != null) {
            Logger.d(TAG, msg = "指尖式数据采集已开启，设备验证通过，连接PPG设备: ${targetDevice.name}")
            connectPpgDevice(targetDevice)
        } else {
            Logger.w(TAG, msg = "未找到匹配的PPG设备，采集器编号: $collectorNumber")
            Toast.makeText(this, "未找到匹配的PPG设备，请检查采集器编号设置", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 从设备名称中提取后六位数字
     * @param deviceName 设备名称
     * @return 后六位数字字符串，如果没有找到则返回空字符串
     */
    private fun extractLastSixDigits(deviceName: String?): String {
        if (deviceName.isNullOrEmpty()) {
            return ""
        }

        // 使用正则表达式提取所有数字
        val digits = deviceName.replace(Regex("[^0-9]"), "")

        // 如果数字长度大于等于6位，取后六位；否则返回空字符串
        return if (digits.length >= 6) {
            digits.takeLast(6)
        } else {
            ""
        }
    }

    fun connectPpgDevice(bluetooth: Bluetooth){
        // set interface before connect
        BleServiceHelper.Companion.BleServiceHelper.setInterfaces(bluetooth.model)
        // add observer(ble state)
        lifecycle.addObserver(BIOL(this, intArrayOf(bluetooth.model)))

        // stop scan before connect
        BleServiceHelper.Companion.BleServiceHelper.stopScan()
        // connect
        BleServiceHelper.Companion.BleServiceHelper.connect(this, bluetooth.model, bluetooth.device)
        BluetoothController.clear()
    }


    private fun completeEvaluation(){
        Logger.d(TAG, msg = "开始完成PPG数据评估，数据点数量: ${ppgDataPointList.size}")
        isComplete = true
        isTerminate = false

        ppgAnalysisResult = PPGManager.analyzeECG(ppgDataPointList)?.apply {
            patientId = mPatientId
        }

        Logger.d(TAG, msg = "PPG数据评估完成，分析结果: ${ppgAnalysisResult != null}")
    }



    private fun initData() {
        userVM.getUserInfo("$mTokenType $mAccessToken")
        userVM.getDetectionProjects("$mTokenType $mAccessToken")
    }

    /**
     * 启动PPG WebSocket服务
     */
    private fun startPpgWebSocketService() {
        lifecycleScope.launch {
            try {
                val success = ppgWebSocketManager.startWebSocketService(this@DetectionActivity1)
                if (success) {
                    Logger.i(TAG, msg = "✓ PPG WebSocket服务启动成功")
                    val address = ppgWebSocketManager.getCurrentWebSocketAddress()
                    Logger.i(TAG, msg = "  WebSocket地址: $address")

                    // 可以通过JavaScript接口将WebSocket地址传递给Web页面
//                    address?.let { wsAddress ->
//                        wbHrv.evaluateJavascript("javascript:setPpgWebSocketAddress('$wsAddress')", null)
//                    }
                } else {
                    Logger.e(TAG, msg = "PPG WebSocket服务启动失败")
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "启动PPG WebSocket服务异常: ${e.message}")
            }
        }
    }

    /**
     * 停止PPG WebSocket服务
     */
    private fun stopPpgWebSocketService() {
        try {
            ppgWebSocketManager.stopWebSocketService()
            Logger.i(TAG, msg = "✓ PPG WebSocket服务已停止")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "停止PPG WebSocket服务异常: ${e.message}")
        }
    }

    /**
     * 广播PPG数据到WebSocket客户端
     */
    private fun broadcastPpgData(ppgDataPoint: PPGDataPoint) {
        try {
            // 构建JSON数据
            val jsonData = buildString {
                append("{")
                append("\"timestamp\":${ppgDataPoint.timestamp},")
                append("\"ppgValue\":${ppgDataPoint.ppgValue},")
                append("\"type\":\"ppg_data\"")
                append("}")
            }

            ppgWebSocketManager.broadcastPpgData(jsonData)
            Logger.d(TAG, msg = "广播PPG数据: $jsonData")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "广播PPG数据异常: ${e.message}")
        }
    }

    /**
     * 广播RtParam数据到WebSocket客户端
     */
    private fun broadcastRtParamData(rtParam: RtParam) {
        try {
            // 构建JSON数据，包含RtParam的主要参数
            val jsonData = buildString {
                append("{")
                append("\"timestamp\":${System.currentTimeMillis()},")
                append("\"type\":\"rt_param\",")
                append("\"data\":{")
                append("\"spo2\":${rtParam.spo2},")
                append("\"pi\":${rtParam.pi},")
                append("\"pr\":${rtParam.pr},")
                append("\"isProbeOff\":${rtParam.isProbeOff},")
                append("\"isPulseSearching\":${rtParam.isPulseSearching}")
                append("}")
                append("}")
            }

            ppgWebSocketManager.broadcastPpgData(jsonData)
            Logger.d(TAG, msg = "广播RtParam数据: $jsonData")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "广播RtParam数据异常: ${e.message}")
        }
    }

    /**
     * 广播蓝牙状态变化到WebSocket客户端
     */
    private fun broadcastBleStateData(model: Int, state: Int) {
        try {
            // 将状态值转换为可读的状态名称
            val stateName = when(state) {
                Ble.State.CONNECTING -> "CONNECTING"
                Ble.State.CONNECTED -> "CONNECTED"
                Ble.State.DISCONNECTING -> "DISCONNECTING"
                Ble.State.DISCONNECTED -> "DISCONNECTED"
                Ble.State.UNKNOWN -> "UNKNOWN"
                else -> "UNKNOWN_STATE_$state"
            }

            // 构建JSON数据
            val jsonData = buildString {
                append("{")
                append("\"timestamp\":${System.currentTimeMillis()},")
                append("\"type\":\"ble_state\",")
                append("\"data\":{")
                append("\"stateName\":\"$stateName\"")
                append("}")
                append("}")
            }

            ppgWebSocketManager.broadcastPpgData(jsonData)
            Logger.d(TAG, msg = "广播蓝牙状态数据: $jsonData")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "广播蓝牙状态数据异常: ${e.message}")
        }
    }

    private fun updateUserInfo(user: User?){
        val userGender = UserManager.getUserGender(user)
        when(userGender){
            Gender.FEMALE ->{
                ImageLoader.loadImageWithPlaceholder(this,user?.avatar?:"",R.drawable.ic_female_avatar_round,R.drawable.ic_female_avatar_round,ivUserAvatar)
            }
            else ->{
                ImageLoader.loadImageWithPlaceholder(this,user?.avatar?:"",R.drawable.ic_male_avatar_round,R.drawable.ic_male_avatar_round,ivUserAvatar)
            }
        }
        tvUserName.isVisible = user?.username.orEmpty().isNotBlank()
        tvUserName.text = user?.username

        tvUserGender.isVisible = userGender != null
        tvUserGender.text = getString(R.string.str_gender_, when(userGender){
            Gender.MALE -> getString(R.string.str_gender_male)
            Gender.FEMALE -> getString(R.string.str_gender_female)
            else -> getString(R.string.str_confidential)
        })

        tvUserPhone.isVisible = user?.phone.orEmpty().isNotBlank()
        tvUserPhone.text = getString(R.string.str_phone_last_number_, user?.phone?:"")

        tvDetectionCode.isVisible = user?.id != null
        tvDetectionCode.text = getString(R.string.str_detection_code_s, user?.id.toString())

        user?.let {
            playPromptVoice(it)
        }
    }

    private fun loadUrl(url:String){
        if (url.isNotBlank()){
            // 在加载URL前清除WebView缓存
            wbHrv.loadUrl(url)
        }
    }

    /**
     * 清除WebView缓存
     */
    private fun clearWebViewCache() {
        wbHrv.clearWebViewCache()
    }

    /**
     * 播放提示语言
     */
    @OptIn(UnstableApi::class)
    private fun playPromptVoice(user: User){
        Logger.d(TAG, msg = "playPromptVoice user = $user")
        val mediaSources = mutableListOf<MediaSource>()
        when(DeviceManager.getStartupMode()){
            StartupMode.QRCODE_WECHAT,StartupMode.QRCODE_H5,StartupMode.QRCODE_WHATSAPP ->{
                val phoneNumList = user.phone.orEmpty().map { it - '0' }
                if (phoneNumList.size != 4) return
                val phoneLastNumSpeech = RawMedia(R.raw.phone_last_number).createMediaSource(this)
                if (phoneLastNumSpeech != null) mediaSources.add(phoneLastNumSpeech)
                phoneNumList.forEach {
                    val phoneNumSpeech = getPhoneNumSpeech(it)
                    if (phoneNumSpeech != null) mediaSources.add(phoneNumSpeech)
                }
            }
            StartupMode.ACCESS_CODE ->{
                val idLastFour = user.id.toString().replace("-", "")
                if (idLastFour.length < 4) return
                val codeLastNumSpeech = RawMedia(R.raw.detection_code_last_number).createMediaSource(this)
                if (codeLastNumSpeech != null) mediaSources.add(codeLastNumSpeech)
                idLastFour.padStart(4, '0')// 补零到4位
                    .takeLast(4)
                    .map { it - '0' }
                    .forEach {
                        val phoneNumSpeech = getPhoneNumSpeech(it)
                        if (phoneNumSpeech != null) mediaSources.add(phoneNumSpeech)
                    }
            }
            else ->{
            }
        }
        val proceedDetection = RawMedia(R.raw.proceed_detection).createMediaSource(this)
        if (proceedDetection != null) mediaSources.add(proceedDetection)
        if (mediaSources.isEmpty()) return
        val concatenatingMediaSource = ConcatenatingMediaSource(*mediaSources.toTypedArray())
        PlayManager.playMediaSource(concatenatingMediaSource)
    }

    private fun getPhoneNumSpeech(phoneNum:Int): MediaSource?{
        return when(phoneNum){
            0 -> return RawMedia(R.raw.speech_0).createMediaSource(this)
            1 -> return RawMedia(R.raw.speech_1).createMediaSource(this)
            2 -> return RawMedia(R.raw.speech_2).createMediaSource(this)
            3 -> return RawMedia(R.raw.speech_3).createMediaSource(this)
            4 -> return RawMedia(R.raw.speech_4).createMediaSource(this)
            5 -> return RawMedia(R.raw.speech_5).createMediaSource(this)
            6 -> return RawMedia(R.raw.speech_6).createMediaSource(this)
            7 -> return RawMedia(R.raw.speech_7).createMediaSource(this)
            8 -> return RawMedia(R.raw.speech_8).createMediaSource(this)
            9 -> return RawMedia(R.raw.speech_9).createMediaSource(this)
            else -> null
        }
    }

    override fun onPageFinished() {
        lifecycleScope.launch {
            llLoading.isVisible = false
        }
    }

    override fun onFinish() {
        lifecycleScope.launch {
            finish()
        }
    }

    override fun goHome() {
        lifecycleScope.launch {
            finish()
        }
    }

    override fun onPrintPage() {
        lifecycleScope.launch {
            createWebPrintJob(wbHrv)
        }
    }

    override fun onReady() {
        lifecycleScope.launch {
            toggleFullScreen(true)
        }
    }

    override fun onStartGazeTracking() {
        Logger.d(TAG, msg = "🚀 DetectionActivity1 - 收到启动眼动追踪请求")
        Logger.d(TAG, msg = "  当前状态: isGazeTrackingActive = $isGazeTrackingActive")

        if (isGazeTrackingActive) {
            Logger.w(TAG, msg = "眼动追踪已经在运行中，忽略重复启动请求")
            return
        }

        lifecycleScope.launch {
            try {
                Logger.d(TAG, msg = "通过消息通信启动相机和眼动追踪...")

                // 检查服务连接状态
                if (mServiceManager == null) {
                    Logger.e(TAG, msg = "服务未连接，无法启动眼动追踪")
                    return@launch
                }

                // 1. 先发送开启相机消息
                val cameraMessage = Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_ON_CAMERA
                    replyTo = mClientMessage
                }

                mServiceManager?.send(cameraMessage)
                Logger.d(TAG, msg = "已发送开启相机消息")

                val trackMessage = Message.obtain().apply {
                    what = GazeConstants.MSG_START_TRACK
                    replyTo = mClientMessage
                }

                mServiceManager?.send(trackMessage)

                Logger.d(TAG, msg = "已发送开始追踪消息")

                // 暂时设置为成功状态，实际状态会通过服务回调更新
                isGazeTrackingActive = true

            } catch (e: Exception) {
                Logger.e(TAG, msg = "启动眼动追踪异常: ${e.message}")
            }
        }
    }

    override fun onStopGazeTracking() {
        Logger.d(TAG, msg = "🛑 DetectionActivity1 - 收到停止眼动追踪请求")
        Logger.d(TAG, msg = "  当前状态: isGazeTrackingActive = $isGazeTrackingActive")

        if (!isGazeTrackingActive) {
            Logger.w(TAG, msg = "眼动追踪未在运行，忽略停止请求")
            return
        }

        lifecycleScope.launch {
            try {
                Logger.d(TAG, msg = "通过消息通信停止眼动追踪和相机...")

                // 检查服务连接状态
                if (mServiceManager == null) {
                    Logger.e(TAG, msg = "服务未连接，无法停止眼动追踪")
                    return@launch
                }

                // 1. 先发送停止追踪消息
                val stopTrackMessage = Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_TRACK
                    replyTo = mClientMessage
                }
                mServiceManager?.send(stopTrackMessage)
                Logger.d(TAG, msg = "已发送停止追踪消息")


                val stopCameraMessage = Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_OFF_CAMERA
                    replyTo = mClientMessage
                }
                mServiceManager?.send(stopCameraMessage)
                Logger.d(TAG, msg = "已发送关闭相机消息")

                // 更新状态
                isGazeTrackingActive = false
                Logger.i(TAG, msg = "✓ 眼动追踪停止消息已发送，状态已更新")

                // 通知Web页面停止成功
                Logger.d(TAG, msg = "已通知Web页面眼动追踪停止")

            } catch (e: Exception) {
                Logger.e(TAG, msg = "停止眼动追踪异常: ${e.message}")
            }
        }
    }

    override fun onGazeTrackingStatus(isEnabled: Boolean) {
        Logger.d(TAG, msg = "⚙️ DetectionActivity1 - 收到眼动追踪状态变化请求: $isEnabled")
        lifecycleScope.launch {
            try {
                // 处理眼动追踪状态变化
                if (isEnabled) {
                    Logger.d(TAG, msg = "状态设置为启用，调用启动方法")
                    onStartGazeTracking()
                } else {
                    Logger.d(TAG, msg = "状态设置为禁用，调用停止方法")
                    onStopGazeTracking()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "处理眼动追踪状态变化异常: ${e.message}")
            }
        }
    }

    override fun generatePpgAnalysisReport(): String? {
        ppgAnalysisResult = PPGManager.analyzeECG(ppgDataPointList)?.apply {
            patientId = mPatientId
        }
        return gson.toJson(ppgAnalysisResult);
    }

    /**
     * 获取当前相机图像
     * 实现HrvActionListener接口
     */
    override fun onRequestCameraImage(): Bitmap? {
        return try {
            Logger.d(TAG, msg = "🔍 请求获取相机图像")

            // 这里可以从相机管理器或图像处理模块获取当前图像
            // 示例：如果有眼动追踪的相机图像，可以从那里获取
            // 或者从其他图像源获取

            // 暂时返回null，实际实现时需要根据具体的相机实现来获取
            Logger.w(TAG, msg = "  相机图像获取功能暂未实现")
            null
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取相机图像异常: ${e.message}")
            null
        }
    }

    /**
     * 根据文件路径获取图片文件
     * 实现HrvActionListener接口
     */
    override fun onRequestImageFile(filePath: String): File? {
        return try {
            Logger.d(TAG, msg = "🔍 请求获取图片文件: $filePath")

            when {
                // 处理素材库图片
                filePath.startsWith("material://") -> {
                    val parts = filePath.removePrefix("material://").split("/")
                    if (parts.size >= 2) {
                        val materialId = parts[0].toLongOrNull()
                        val fileName = parts[1]
                        if (materialId != null) {
                            val imageFile = MaterialHelper.getMaterialImageByName(this, materialId, fileName)
                            Logger.d(TAG, msg = "  素材库图片: $imageFile")
                            imageFile
                        } else {
                            Logger.w(TAG, msg = "  无效的素材库ID: ${parts[0]}")
                            null
                        }
                    } else {
                        Logger.w(TAG, msg = "  无效的素材库路径格式: $filePath")
                        null
                    }
                }

                // 处理绝对路径
                filePath.startsWith("/") -> {
                    val absoluteFile = File(filePath)
                    if (absoluteFile.exists()) {
                        Logger.d(TAG, msg = "  绝对路径图片: ${absoluteFile.absolutePath}")
                        absoluteFile
                    } else {
                        Logger.w(TAG, msg = "  绝对路径图片不存在: $filePath")
                        null
                    }
                }

                // 处理相对路径（相对于应用数据目录）
                else -> {
                    val relativeFile = File(filesDir, filePath)
                    if (relativeFile.exists()) {
                        Logger.d(TAG, msg = "  相对路径图片: ${relativeFile.absolutePath}")
                        relativeFile
                    } else {
                        Logger.w(TAG, msg = "  相对路径图片不存在: ${relativeFile.absolutePath}")
                        null
                    }
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取图片文件异常: $filePath - ${e.message}")
            null
        }
    }

    /**
     * 批量获取图片
     * 实现HrvActionListener接口
     */
    override fun onRequestBatchImages(imageIds: List<String>): List<Bitmap>? {
        return try {
            Logger.d(TAG, msg = "🔍 请求批量获取图片，数量: ${imageIds.size}")
            Logger.d(TAG, msg = "  图片ID列表: $imageIds")

            val bitmaps = mutableListOf<Bitmap>()

            imageIds.forEach { imageId ->
                val bitmap = when {
                    // 从素材库获取
                    imageId.startsWith("material_") -> {
                        Logger.d(TAG, msg = "  处理素材库图片: $imageId")
                        loadMaterialImageBitmap(imageId)
                    }

                    // 从文件系统获取
                    imageId.startsWith("file_") -> {
                        Logger.d(TAG, msg = "  处理文件系统图片: $imageId")
                        loadFileImageBitmap(imageId)
                    }


                    else -> {
                        Logger.w(TAG, msg = "  未知图片ID格式: $imageId")
                        null
                    }
                }

                bitmap?.let { bitmaps.add(it) }
            }

            Logger.d(TAG, msg = "  成功获取${bitmaps.size}张图片")
            bitmaps
        } catch (e: Exception) {
            Logger.e(TAG, msg = "批量获取图片异常: ${e.message}")
            null
        }
    }

    /**
     * 从素材库加载图片Bitmap
     */
    private fun loadMaterialImageBitmap(imageId: String): Bitmap? {
        return try {
            // 解析素材库图片ID，格式：material_materialId_fileName
            val parts = imageId.split("_")
            if (parts.size >= 3) {
                val materialId = parts[1].toLongOrNull()
                val fileName = parts.drop(2).joinToString("_")
                if (materialId != null) {
                    val bitmap = MaterialHelper.loadMaterialImageBitmap(this, materialId, fileName)
                    Logger.d(TAG, msg = "    素材库图片加载成功: $imageId")
                    bitmap
                } else {
                    Logger.w(TAG, msg = "    无效的素材库ID: ${parts[1]}")
                    null
                }
            } else {
                Logger.w(TAG, msg = "    无效的素材库图片ID格式: $imageId")
                null
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "    加载素材库图片失败: $imageId - ${e.message}")
            null
        }
    }

    /**
     * 从文件系统加载图片Bitmap
     */
    private fun loadFileImageBitmap(imageId: String): Bitmap? {
        return try {
            // 解析文件图片ID，格式：file_actualFilePath（用_替换/）
            val filePath = imageId.removePrefix("file_").replace("_", "/")
            val file = File(filePath)
            if (file.exists()) {
                val bitmap = BitmapFactory.decodeFile(file.absolutePath)
                Logger.d(TAG, msg = "    文件图片加载成功: $imageId")
                bitmap
            } else {
                Logger.w(TAG, msg = "    文件不存在: $filePath")
                null
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "    加载文件图片失败: $imageId - ${e.message}")
            null
        }
    }


    private fun createWebPrintJob(webView: WebView) {
        val printManager = getSystemService(PRINT_SERVICE) as PrintManager
        val jobName = getString(R.string.app_name) + " Document"

        val printAdapter = webView.createPrintDocumentAdapter(jobName)

        printManager.print(
            jobName, printAdapter,
            PrintAttributes.Builder()
                .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
                .setResolution(PrintAttributes.Resolution("pdf", "pdf", 600, 600))
                .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                .build()
        )
    }

    /**
     * 切换全屏
     * @param isFUll true 全屏，false 退出全屏
     */
    private fun toggleFullScreen(isFUll:Boolean){
        if (isFUll){
            //切成全屏
            val constraintSet = ConstraintSet()
            constraintSet.clone(clDetectionRoot)
            constraintSet.clear(R.id.cl_content)
            constraintSet.connect(R.id.cl_content, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
            constraintSet.connect(R.id.cl_content, ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT)
            constraintSet.connect(R.id.cl_content, ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT)
            constraintSet.setMargin(R.id.cl_content, ConstraintSet.TOP, 0)
            constraintSet.setMargin(R.id.cl_content, ConstraintSet.END, 0)
            constraintSet.applyTo(clDetectionRoot)

            val params = clContent.layoutParams as LayoutParams
            params.width = LayoutParams.MATCH_PARENT
            params.height = LayoutParams.MATCH_PARENT
            clContent.layoutParams = params

        }else{
            //切成半屏
            val constraintSet = ConstraintSet()
            constraintSet.clone(clDetectionRoot)
            constraintSet.clear(R.id.cl_content)
            constraintSet.connect(R.id.cl_content, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
            constraintSet.connect(R.id.cl_content, ConstraintSet.LEFT, R.id.iv_user_info_bg, ConstraintSet.RIGHT)
            constraintSet.connect(R.id.cl_content, ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT)
            constraintSet.setMargin(R.id.cl_content, ConstraintSet.TOP, 90.dp2px(this))
            constraintSet.setMargin(R.id.cl_content, ConstraintSet.END, 20.dp2px(this))
            constraintSet.applyTo(clDetectionRoot)

            val params = clContent.layoutParams as LayoutParams
            params.width = 0
            params.height = 430.dp2px(this)
            clContent.layoutParams = params
        }
    }

    override fun onPause() {
        super.onPause()
        wbHrv.onPause()
        wbHrv.pauseTimers()
    }

    override fun onResume() {
        super.onResume()
        wbHrv.resumeTimers()
        wbHrv.onResume()
    }

    override fun onStart() {
        super.onStart()
        Logger.d(TAG, msg = "🔄 DetectionActivity1 - onStart() 开始")

        // 绑定到独立进程服务
        val intent = Intent(this, GazeTrackService::class.java)
        val result = bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE or Context.BIND_IMPORTANT)
        Logger.d(TAG, msg = "绑定独立进程GazeTrackService结果: $result")

        // 注意：开启相机消息现在在 serviceConnection.onServiceConnected() 回调中发送
        // 这样可以确保服务连接完成后立即发送，避免使用 delay 的副作用
    }

    override fun onStop() {
        super.onStop()
        Logger.d(TAG, msg = "🛑 DetectionActivity1 - onStop() 开始")

        // 发送关闭相机消息
        if (mServiceManager != null) {
            Logger.d(TAG, msg = "📷 发送关闭相机消息")
            val cameraMessage = Message.obtain().apply {
                what = GazeConstants.MSG_TURN_OFF_CAMERA
                replyTo = mClientMessage
            }
            mServiceManager?.send(cameraMessage)
            Logger.d(TAG, msg = "✓ 已发送关闭相机消息")
        }

        // 解绑服务连接
        try {
            Logger.d(TAG, msg = "解绑GazeTrackService服务连接")
            unbindService(serviceConnection)
            mServiceManager = null
            Logger.d(TAG, msg = "✓ GazeTrackService服务连接已解绑")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "解绑GazeTrackService服务连接异常: ${e.message}")
        }
    }



    override fun onDestroy() {
        Logger.d(TAG, msg = "📱 Activity onDestroy - 清理眼动追踪资源")
        Logger.d(TAG, msg = "  当前眼动追踪状态: $isGazeTrackingActive")

        // 确保清理眼动追踪资源
        if (isGazeTrackingActive) {
            Logger.d(TAG, msg = "  调用onStopGazeTracking清理眼动追踪资源")
            try {
                // 调用完整的停止流程，确保相机和追踪服务正确停止
                onStopGazeTracking()
                Logger.d(TAG, msg = "  ✓ 眼动追踪资源已清理")
            } catch (e: Exception) {
                Logger.e(TAG, msg = "  清理眼动追踪资源异常: ${e.message}")
                // 如果onStopGazeTracking失败，至少更新本地状态
                isGazeTrackingActive = false
            }
        }
        // 解绑服务连接
        try {
            Logger.d(TAG, msg = "  解绑GazeTrackService服务连接")
            unbindService(serviceConnection)
            mServiceManager = null
            Logger.d(TAG, msg = "  ✓ GazeTrackService服务连接已解绑")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "  解绑GazeTrackService服务连接异常: ${e.message}")
        }

        // 停止GazeTrackService前台服务以释放资源
        try {
            Logger.d(TAG, msg = "  停止GazeTrackService前台服务")
            stopService(Intent(this, GazeTrackService::class.java))
            Logger.d(TAG, msg = "  ✓ GazeTrackService服务已停止")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "  停止GazeTrackService服务异常: ${e.message}")
        }

        // 停止PPG WebSocket服务
        try {
            Logger.d(TAG, msg = "  停止PPG WebSocket服务")
            stopPpgWebSocketService()
            Logger.d(TAG, msg = "  ✓ PPG WebSocket服务已停止")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "  停止PPG WebSocket服务异常: ${e.message}")
        }

        wbHrv.clearWebViewCache()
        wbHrv.destroy()
        super.onDestroy()
        Logger.d(TAG, msg = "📱 Activity onDestroy 完成")
    }

    override fun onBleStateChanged(model: Int, state: Int) {
//        ppgVM.setBleState(state)
        Logger.d(TAG, msg = "蓝牙状态变化: model=$model, state=$state")

        // 广播蓝牙状态变化到WebSocket客户端
        broadcastBleStateData(model, state)

        when(state){
            Ble.State.CONNECTING -> {
                Logger.d(TAG, msg = "蓝牙状态变化CONNECTING: model=$model, state=$state")
            }
            Ble.State.CONNECTED -> {
                Logger.d(TAG, msg = "蓝牙状态变化CONNECTED: model=$model, state=$state")
            }
            Ble.State.DISCONNECTING -> {
                Logger.d(TAG, msg = "蓝牙状态变化DISCONNECTING: model=$model, state=$state")
            }
            Ble.State.DISCONNECTED -> {
                Logger.d(TAG, msg = "蓝牙状态变化DISCONNECTED: model=$model, state=$state")
            }
            Ble.State.UNKNOWN -> {
                Logger.d(TAG, msg = "蓝牙状态变化UNKNOWN: model=$model, state=$state")
            }
            else -> {
            }
        }
    }

}